# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

HalalPlus is a modern microservices platform built with GoFrame, gRPC, and APISIX for halal financial services, Islamic content management, and e-commerce. The platform supports high concurrency, high availability, and flexible scaling for financial and payment scenarios requiring strict data consistency and security.

## Architecture

The project is organized as a microservices architecture with the following core services:

- **user-account-svc**: User management, authentication, KYC, account info, payments binding
- **payment-svc**: Payment processing, transfers, transactions, bill management, risk control
- **wealth-charity-svc**: Investment products, charity donations, Zakat calculations
- **mall-svc**: Product catalog, orders, coupons, merchant management, shopping cart
- **islamic-content-svc**: Quran content, prayer times, news, articles, Islamic calendar
- **file-storage-svc**: File upload/download, image/audio/video storage (AWS S3/MinIO)
- **notify-svc**: SMS, email, push notifications with queue management

## Development Commands

### Prerequisites
```bash
# Install GoFrame CLI (required for most commands)
make cli.install
```

### Core Development Commands
```bash
# Generate Protocol Buffer files for all services
sh gf-gen-pb.sh

# Generate service interfaces after adding logic
gf gen service

# Generate DAO/DO/Entity files from database
gf gen dao

# Generate controllers from API definitions
gf gen ctrl

# Build the application
gf build -ew

# Run all microservices
sh gf-run-main.sh

# Run individual service (from service directory)
gf run main.go
```

### Testing
```bash
# Test gRPC services with grpcurl
grpcurl -plaintext -d '{"account":"testuser","password":"123456"}' 127.0.0.1:9200 user.v1.UserService/SignIn

# Run tests from service directories
go test ./...
```

### Docker & Deployment
```bash
# Build Docker image
make image

# Build and push to registry
make image.push

# Deploy to Kubernetes
make deploy
```

## Project Structure

### Root Level
- `app/` - All microservices
- `manifest/` - Deployment configs, protobuf schemas, SQL migrations
- `utility/` - Shared utilities and libraries
- `apiDoc/` - Generated API documentation
- `hack/` - Build and development scripts

### Service Structure (each service follows this pattern)
```
app/{service-name}/
├── api/           # Generated gRPC code and proto definitions
├── internal/      # Service implementation
│   ├── cmd/       # Application entry point
│   ├── controller/# gRPC controllers
│   ├── logic/     # Business logic layer
│   ├── service/   # Service interfaces
│   ├── dao/       # Data access objects
│   ├── model/     # Data models (do/, entity/)
│   └── consts/    # Constants
├── manifest/      # Config, protobuf, SQL, Docker
├── main.go        # Service entry point
└── Makefile       # Service-specific build commands
```

## Key Technologies

- **GoFrame v2.9**: Go web framework for microservices
- **gRPC + Protocol Buffers**: Inter-service communication
- **MySQL**: Primary database
- **Redis**: Caching, sessions, distributed locks
- **Consul**: Service discovery and configuration
- **AWS SQS**: Message queuing
- **AWS S3/MinIO**: File storage
- **APISIX**: API Gateway

## Development Workflow

1. **Protocol Definition**: Define APIs in `manifest/protobuf/`
2. **Code Generation**: Run `sh gf-gen-pb.sh` after proto changes
3. **Business Logic**: Implement in `internal/logic/{module}/{module}.go`
4. **Service Update**: Run `gf gen service` to update interfaces
5. **Database Changes**: Record migrations in `manifest/sql/`
6. **Testing**: Use grpcurl for gRPC endpoint testing
7. **Gateway**: Configure APISIX with proto and route settings

## Important Notes

- Always run `sh gf-gen-pb.sh` from the root directory after modifying any .proto files
- Each service has its own database migrations in `manifest/sql/`
- Services communicate via gRPC; HTTP endpoints are exposed through APISIX gateway
- All services follow GoFrame conventions for dependency injection and lifecycle management
- JWT tokens are used for authentication across services
- File uploads go through file-storage-svc which handles AWS S3/MinIO integration

## Testing Services

Use grpcurl for testing gRPC endpoints directly:
```bash
# Example: Test user sign-in
grpcurl -plaintext -d '{"account":"testuser","password":"123456"}' 127.0.0.1:9200 user.v1.UserService/SignIn

# List available services
grpcurl -plaintext 127.0.0.1:9200 list

# List methods for a service
grpcurl -plaintext 127.0.0.1:9200 list user.v1.UserService
```

## Common Patterns

- **Service Registration**: Each service registers with Consul for discovery
- **Error Handling**: Use GoFrame's gerror with proper error codes
- **Logging**: Use GoFrame's g.Log() with context
- **Database Access**: Use GoFrame's ORM through DAO pattern
- **Configuration**: Managed through Consul and local config files
- **Authentication**: JWT tokens validated through utility/token package