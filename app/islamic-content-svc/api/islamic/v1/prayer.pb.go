// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: islamic/v1/prayer.proto

package islamicv1

import (
	common "halalplus/api/common"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 祷告时间查询请求
type GetPrayerTimesReq struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Latitude          float64                `protobuf:"fixed64,1,opt,name=latitude,proto3" json:"latitude,omitempty" dc:"纬度"`                                                                                  // 纬度
	Longitude         float64                `protobuf:"fixed64,2,opt,name=longitude,proto3" json:"longitude,omitempty" dc:"经度"`                                                                                // 经度
	Date              string                 `protobuf:"bytes,3,opt,name=date,proto3" json:"date,omitempty" dc:"日期 YYYY-MM-DD 格式"`                                                                              // 日期 YYYY-MM-DD 格式
	Timezone          string                 `protobuf:"bytes,4,opt,name=timezone,proto3" json:"timezone,omitempty" dc:"时区，如 'Asia/Shanghai'"`                                                                  // 时区，如 "Asia/Shanghai"
	CalculationMethod string                 `protobuf:"bytes,5,opt,name=calculation_method,json=calculationMethod,proto3" json:"calculation_method,omitempty" dc:"计算方法: 'MWL', 'ISNA', 'Egyptian', 'Karachi'"` // 计算方法: "MWL", "ISNA", "Egyptian", "Karachi"
	FajrAngle         float64                `protobuf:"fixed64,6,opt,name=fajr_angle,json=fajrAngle,proto3" json:"fajr_angle,omitempty" dc:"晨祷角度，默认18.0"`                                                      // 晨祷角度，默认18.0
	IshaAngle         float64                `protobuf:"fixed64,7,opt,name=isha_angle,json=ishaAngle,proto3" json:"isha_angle,omitempty" dc:"宵祷角度，默认17.0"`                                                      // 宵祷角度，默认17.0
	AsrMethod         string                 `protobuf:"bytes,8,opt,name=asr_method,json=asrMethod,proto3" json:"asr_method,omitempty" dc:"晡祷计算方法: 'Standard' 或 'Hanafi'"`                                      // 晡祷计算方法: "Standard" 或 "Hanafi"
	Elevation         float64                `protobuf:"fixed64,9,opt,name=elevation,proto3" json:"elevation,omitempty" dc:"海拔高度（米），可选"`                                                                        // 海拔高度（米），可选
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetPrayerTimesReq) Reset() {
	*x = GetPrayerTimesReq{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPrayerTimesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPrayerTimesReq) ProtoMessage() {}

func (x *GetPrayerTimesReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPrayerTimesReq.ProtoReflect.Descriptor instead.
func (*GetPrayerTimesReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{0}
}

func (x *GetPrayerTimesReq) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *GetPrayerTimesReq) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *GetPrayerTimesReq) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *GetPrayerTimesReq) GetTimezone() string {
	if x != nil {
		return x.Timezone
	}
	return ""
}

func (x *GetPrayerTimesReq) GetCalculationMethod() string {
	if x != nil {
		return x.CalculationMethod
	}
	return ""
}

func (x *GetPrayerTimesReq) GetFajrAngle() float64 {
	if x != nil {
		return x.FajrAngle
	}
	return 0
}

func (x *GetPrayerTimesReq) GetIshaAngle() float64 {
	if x != nil {
		return x.IshaAngle
	}
	return 0
}

func (x *GetPrayerTimesReq) GetAsrMethod() string {
	if x != nil {
		return x.AsrMethod
	}
	return ""
}

func (x *GetPrayerTimesReq) GetElevation() float64 {
	if x != nil {
		return x.Elevation
	}
	return 0
}

// 祷告时间查询响应
type GetPrayerTimesRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *PrayerTimesData       `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPrayerTimesRes) Reset() {
	*x = GetPrayerTimesRes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPrayerTimesRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPrayerTimesRes) ProtoMessage() {}

func (x *GetPrayerTimesRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPrayerTimesRes.ProtoReflect.Descriptor instead.
func (*GetPrayerTimesRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{1}
}

func (x *GetPrayerTimesRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetPrayerTimesRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetPrayerTimesRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *GetPrayerTimesRes) GetData() *PrayerTimesData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 祷告时间数据
type PrayerTimesData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PrayerTimes   *PrayerTimes           `protobuf:"bytes,1,opt,name=prayer_times,json=prayerTimes,proto3" json:"prayer_times,omitempty" dc:"祷告时间"`         // 祷告时间
	Location      *LocationInfo          `protobuf:"bytes,2,opt,name=location,proto3" json:"location,omitempty" dc:"位置信息"`                                  // 位置信息
	IslamicDate   *IslamicDate           `protobuf:"bytes,3,opt,name=islamic_date,json=islamicDate,proto3" json:"islamic_date,omitempty" dc:"伊斯兰历日期"`       // 伊斯兰历日期
	IsRamadhan    bool                   `protobuf:"varint,4,opt,name=is_ramadhan,json=isRamadhan,proto3" json:"is_ramadhan,omitempty" dc:"是否斋月"`           // 是否斋月
	CurrentPrayer string                 `protobuf:"bytes,5,opt,name=current_prayer,json=currentPrayer,proto3" json:"current_prayer,omitempty" dc:"当前祷告名称"` // 当前祷告名称
	NextPrayer    string                 `protobuf:"bytes,6,opt,name=next_prayer,json=nextPrayer,proto3" json:"next_prayer,omitempty" dc:"下一个祷告名称"`         // 下一个祷告名称
	TimeToNext    int32                  `protobuf:"varint,7,opt,name=time_to_next,json=timeToNext,proto3" json:"time_to_next,omitempty" dc:"距离下个祷告的秒数"`    // 距离下个祷告的秒数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PrayerTimesData) Reset() {
	*x = PrayerTimesData{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PrayerTimesData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrayerTimesData) ProtoMessage() {}

func (x *PrayerTimesData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrayerTimesData.ProtoReflect.Descriptor instead.
func (*PrayerTimesData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{2}
}

func (x *PrayerTimesData) GetPrayerTimes() *PrayerTimes {
	if x != nil {
		return x.PrayerTimes
	}
	return nil
}

func (x *PrayerTimesData) GetLocation() *LocationInfo {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *PrayerTimesData) GetIslamicDate() *IslamicDate {
	if x != nil {
		return x.IslamicDate
	}
	return nil
}

func (x *PrayerTimesData) GetIsRamadhan() bool {
	if x != nil {
		return x.IsRamadhan
	}
	return false
}

func (x *PrayerTimesData) GetCurrentPrayer() string {
	if x != nil {
		return x.CurrentPrayer
	}
	return ""
}

func (x *PrayerTimesData) GetNextPrayer() string {
	if x != nil {
		return x.NextPrayer
	}
	return ""
}

func (x *PrayerTimesData) GetTimeToNext() int32 {
	if x != nil {
		return x.TimeToNext
	}
	return 0
}

// 祷告时间
type PrayerTimes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Imsak         string                 `protobuf:"bytes,1,opt,name=imsak,proto3" json:"imsak,omitempty" dc:"伊姆萨克时间（仅斋月期间）"` // 伊姆萨克时间（仅斋月期间）
	Subuh         string                 `protobuf:"bytes,2,opt,name=subuh,proto3" json:"subuh,omitempty" dc:"晨祷时间"`          // 晨祷时间
	Terbit        string                 `protobuf:"bytes,3,opt,name=terbit,proto3" json:"terbit,omitempty" dc:"日出时间"`        // 日出时间
	Dhuha         string                 `protobuf:"bytes,4,opt,name=dhuha,proto3" json:"dhuha,omitempty" dc:"上午祷告时间（可选）"`    // 上午祷告时间（可选）
	Zuhur         string                 `protobuf:"bytes,5,opt,name=zuhur,proto3" json:"zuhur,omitempty" dc:"晌祷时间"`          // 晌祷时间
	Ashar         string                 `protobuf:"bytes,6,opt,name=ashar,proto3" json:"ashar,omitempty" dc:"晡祷时间"`          // 晡祷时间
	Maghrib       string                 `protobuf:"bytes,7,opt,name=maghrib,proto3" json:"maghrib,omitempty" dc:"昏祷时间"`      // 昏祷时间
	Isya          string                 `protobuf:"bytes,8,opt,name=isya,proto3" json:"isya,omitempty" dc:"宵祷时间"`            // 宵祷时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PrayerTimes) Reset() {
	*x = PrayerTimes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PrayerTimes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrayerTimes) ProtoMessage() {}

func (x *PrayerTimes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrayerTimes.ProtoReflect.Descriptor instead.
func (*PrayerTimes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{3}
}

func (x *PrayerTimes) GetImsak() string {
	if x != nil {
		return x.Imsak
	}
	return ""
}

func (x *PrayerTimes) GetSubuh() string {
	if x != nil {
		return x.Subuh
	}
	return ""
}

func (x *PrayerTimes) GetTerbit() string {
	if x != nil {
		return x.Terbit
	}
	return ""
}

func (x *PrayerTimes) GetDhuha() string {
	if x != nil {
		return x.Dhuha
	}
	return ""
}

func (x *PrayerTimes) GetZuhur() string {
	if x != nil {
		return x.Zuhur
	}
	return ""
}

func (x *PrayerTimes) GetAshar() string {
	if x != nil {
		return x.Ashar
	}
	return ""
}

func (x *PrayerTimes) GetMaghrib() string {
	if x != nil {
		return x.Maghrib
	}
	return ""
}

func (x *PrayerTimes) GetIsya() string {
	if x != nil {
		return x.Isya
	}
	return ""
}

// 位置信息
type LocationInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Latitude      float64                `protobuf:"fixed64,1,opt,name=latitude,proto3" json:"latitude,omitempty" dc:"纬度"`                          // 纬度
	Longitude     float64                `protobuf:"fixed64,2,opt,name=longitude,proto3" json:"longitude,omitempty" dc:"经度"`                        // 经度
	CityName      string                 `protobuf:"bytes,3,opt,name=city_name,json=cityName,proto3" json:"city_name,omitempty" dc:"城市名称"`          // 城市名称
	CountryName   string                 `protobuf:"bytes,4,opt,name=country_name,json=countryName,proto3" json:"country_name,omitempty" dc:"国家名称"` // 国家名称
	Timezone      string                 `protobuf:"bytes,5,opt,name=timezone,proto3" json:"timezone,omitempty" dc:"时区"`                            // 时区
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LocationInfo) Reset() {
	*x = LocationInfo{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LocationInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationInfo) ProtoMessage() {}

func (x *LocationInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationInfo.ProtoReflect.Descriptor instead.
func (*LocationInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{4}
}

func (x *LocationInfo) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *LocationInfo) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *LocationInfo) GetCityName() string {
	if x != nil {
		return x.CityName
	}
	return ""
}

func (x *LocationInfo) GetCountryName() string {
	if x != nil {
		return x.CountryName
	}
	return ""
}

func (x *LocationInfo) GetTimezone() string {
	if x != nil {
		return x.Timezone
	}
	return ""
}

// 伊斯兰历日期
type IslamicDate struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Year          int32                  `protobuf:"varint,1,opt,name=year,proto3" json:"year,omitempty" dc:"伊斯兰历年份"`                         // 伊斯兰历年份
	Month         int32                  `protobuf:"varint,2,opt,name=month,proto3" json:"month,omitempty" dc:"伊斯兰历月份"`                       // 伊斯兰历月份
	Day           int32                  `protobuf:"varint,3,opt,name=day,proto3" json:"day,omitempty" dc:"伊斯兰历日期"`                           // 伊斯兰历日期
	MonthName     string                 `protobuf:"bytes,4,opt,name=month_name,json=monthName,proto3" json:"month_name,omitempty" dc:"月份名称"` // 月份名称
	Formatted     string                 `protobuf:"bytes,5,opt,name=formatted,proto3" json:"formatted,omitempty" dc:"格式化的日期字符串"`             // 格式化的日期字符串
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IslamicDate) Reset() {
	*x = IslamicDate{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IslamicDate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IslamicDate) ProtoMessage() {}

func (x *IslamicDate) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IslamicDate.ProtoReflect.Descriptor instead.
func (*IslamicDate) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{5}
}

func (x *IslamicDate) GetYear() int32 {
	if x != nil {
		return x.Year
	}
	return 0
}

func (x *IslamicDate) GetMonth() int32 {
	if x != nil {
		return x.Month
	}
	return 0
}

func (x *IslamicDate) GetDay() int32 {
	if x != nil {
		return x.Day
	}
	return 0
}

func (x *IslamicDate) GetMonthName() string {
	if x != nil {
		return x.MonthName
	}
	return ""
}

func (x *IslamicDate) GetFormatted() string {
	if x != nil {
		return x.Formatted
	}
	return ""
}

// 获取用户祷告设置请求
type GetUserPrayerSettingsReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty" dc:"用户ID"` // 用户ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserPrayerSettingsReq) Reset() {
	*x = GetUserPrayerSettingsReq{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserPrayerSettingsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserPrayerSettingsReq) ProtoMessage() {}

func (x *GetUserPrayerSettingsReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserPrayerSettingsReq.ProtoReflect.Descriptor instead.
func (*GetUserPrayerSettingsReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{6}
}

func (x *GetUserPrayerSettingsReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

// 获取用户祷告设置响应
type GetUserPrayerSettingsRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *UserPrayerSettings    `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserPrayerSettingsRes) Reset() {
	*x = GetUserPrayerSettingsRes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserPrayerSettingsRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserPrayerSettingsRes) ProtoMessage() {}

func (x *GetUserPrayerSettingsRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserPrayerSettingsRes.ProtoReflect.Descriptor instead.
func (*GetUserPrayerSettingsRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{7}
}

func (x *GetUserPrayerSettingsRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetUserPrayerSettingsRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetUserPrayerSettingsRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *GetUserPrayerSettingsRes) GetData() *UserPrayerSettings {
	if x != nil {
		return x.Data
	}
	return nil
}

// 用户祷告设置
type UserPrayerSettings struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	UserId            int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty" dc:"用户ID"`                                 // 用户ID
	CalculationMethod string                 `protobuf:"bytes,2,opt,name=calculation_method,json=calculationMethod,proto3" json:"calculation_method,omitempty" dc:"计算方法"` // 计算方法
	FajrAngle         float64                `protobuf:"fixed64,3,opt,name=fajr_angle,json=fajrAngle,proto3" json:"fajr_angle,omitempty" dc:"晨祷角度"`                       // 晨祷角度
	IshaAngle         float64                `protobuf:"fixed64,4,opt,name=isha_angle,json=ishaAngle,proto3" json:"isha_angle,omitempty" dc:"宵祷角度"`                       // 宵祷角度
	AsrMethod         string                 `protobuf:"bytes,5,opt,name=asr_method,json=asrMethod,proto3" json:"asr_method,omitempty" dc:"晡祷计算方法"`                       // 晡祷计算方法
	DefaultLocation   *LocationInfo          `protobuf:"bytes,6,opt,name=default_location,json=defaultLocation,proto3" json:"default_location,omitempty" dc:"默认位置"`       // 默认位置
	Notifications     []*NotificationSetting `protobuf:"bytes,7,rep,name=notifications,proto3" json:"notifications,omitempty" dc:"通知设置"`                                  // 通知设置
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *UserPrayerSettings) Reset() {
	*x = UserPrayerSettings{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserPrayerSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserPrayerSettings) ProtoMessage() {}

func (x *UserPrayerSettings) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserPrayerSettings.ProtoReflect.Descriptor instead.
func (*UserPrayerSettings) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{8}
}

func (x *UserPrayerSettings) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserPrayerSettings) GetCalculationMethod() string {
	if x != nil {
		return x.CalculationMethod
	}
	return ""
}

func (x *UserPrayerSettings) GetFajrAngle() float64 {
	if x != nil {
		return x.FajrAngle
	}
	return 0
}

func (x *UserPrayerSettings) GetIshaAngle() float64 {
	if x != nil {
		return x.IshaAngle
	}
	return 0
}

func (x *UserPrayerSettings) GetAsrMethod() string {
	if x != nil {
		return x.AsrMethod
	}
	return ""
}

func (x *UserPrayerSettings) GetDefaultLocation() *LocationInfo {
	if x != nil {
		return x.DefaultLocation
	}
	return nil
}

func (x *UserPrayerSettings) GetNotifications() []*NotificationSetting {
	if x != nil {
		return x.Notifications
	}
	return nil
}

// 更新用户祷告设置请求
type UpdateUserPrayerSettingsReq struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	UserId            int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty" dc:"用户ID"`                                 // 用户ID
	CalculationMethod string                 `protobuf:"bytes,2,opt,name=calculation_method,json=calculationMethod,proto3" json:"calculation_method,omitempty" dc:"计算方法"` // 计算方法
	FajrAngle         float64                `protobuf:"fixed64,3,opt,name=fajr_angle,json=fajrAngle,proto3" json:"fajr_angle,omitempty" dc:"晨祷角度"`                       // 晨祷角度
	IshaAngle         float64                `protobuf:"fixed64,4,opt,name=isha_angle,json=ishaAngle,proto3" json:"isha_angle,omitempty" dc:"宵祷角度"`                       // 宵祷角度
	AsrMethod         string                 `protobuf:"bytes,5,opt,name=asr_method,json=asrMethod,proto3" json:"asr_method,omitempty" dc:"晡祷计算方法"`                       // 晡祷计算方法
	DefaultLocation   *LocationInfo          `protobuf:"bytes,6,opt,name=default_location,json=defaultLocation,proto3" json:"default_location,omitempty" dc:"默认位置"`       // 默认位置
	Notifications     []*NotificationSetting `protobuf:"bytes,7,rep,name=notifications,proto3" json:"notifications,omitempty" dc:"通知设置"`                                  // 通知设置
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *UpdateUserPrayerSettingsReq) Reset() {
	*x = UpdateUserPrayerSettingsReq{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserPrayerSettingsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserPrayerSettingsReq) ProtoMessage() {}

func (x *UpdateUserPrayerSettingsReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserPrayerSettingsReq.ProtoReflect.Descriptor instead.
func (*UpdateUserPrayerSettingsReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateUserPrayerSettingsReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UpdateUserPrayerSettingsReq) GetCalculationMethod() string {
	if x != nil {
		return x.CalculationMethod
	}
	return ""
}

func (x *UpdateUserPrayerSettingsReq) GetFajrAngle() float64 {
	if x != nil {
		return x.FajrAngle
	}
	return 0
}

func (x *UpdateUserPrayerSettingsReq) GetIshaAngle() float64 {
	if x != nil {
		return x.IshaAngle
	}
	return 0
}

func (x *UpdateUserPrayerSettingsReq) GetAsrMethod() string {
	if x != nil {
		return x.AsrMethod
	}
	return ""
}

func (x *UpdateUserPrayerSettingsReq) GetDefaultLocation() *LocationInfo {
	if x != nil {
		return x.DefaultLocation
	}
	return nil
}

func (x *UpdateUserPrayerSettingsReq) GetNotifications() []*NotificationSetting {
	if x != nil {
		return x.Notifications
	}
	return nil
}

// 更新用户祷告设置响应
type UpdateUserPrayerSettingsRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Message       string                 `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty" dc:"操作结果消息"` // 操作结果消息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserPrayerSettingsRes) Reset() {
	*x = UpdateUserPrayerSettingsRes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserPrayerSettingsRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserPrayerSettingsRes) ProtoMessage() {}

func (x *UpdateUserPrayerSettingsRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserPrayerSettingsRes.ProtoReflect.Descriptor instead.
func (*UpdateUserPrayerSettingsRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateUserPrayerSettingsRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UpdateUserPrayerSettingsRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UpdateUserPrayerSettingsRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *UpdateUserPrayerSettingsRes) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 通知设置
type NotificationSetting struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PrayerName    string                 `protobuf:"bytes,1,opt,name=prayer_name,json=prayerName,proto3" json:"prayer_name,omitempty" dc:"祷告名称"`              // 祷告名称
	Enabled       bool                   `protobuf:"varint,2,opt,name=enabled,proto3" json:"enabled,omitempty" dc:"是否启用"`                                     // 是否启用
	MinutesBefore int32                  `protobuf:"varint,3,opt,name=minutes_before,json=minutesBefore,proto3" json:"minutes_before,omitempty" dc:"提前通知分钟数"` // 提前通知分钟数
	SoundType     string                 `protobuf:"bytes,4,opt,name=sound_type,json=soundType,proto3" json:"sound_type,omitempty" dc:"声音类型"`                 // 声音类型
	Vibration     bool                   `protobuf:"varint,5,opt,name=vibration,proto3" json:"vibration,omitempty" dc:"是否震动"`                                 // 是否震动
	OnlyRamadhan  bool                   `protobuf:"varint,6,opt,name=only_ramadhan,json=onlyRamadhan,proto3" json:"only_ramadhan,omitempty" dc:"仅斋月期间"`      // 仅斋月期间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NotificationSetting) Reset() {
	*x = NotificationSetting{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NotificationSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationSetting) ProtoMessage() {}

func (x *NotificationSetting) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationSetting.ProtoReflect.Descriptor instead.
func (*NotificationSetting) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{11}
}

func (x *NotificationSetting) GetPrayerName() string {
	if x != nil {
		return x.PrayerName
	}
	return ""
}

func (x *NotificationSetting) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *NotificationSetting) GetMinutesBefore() int32 {
	if x != nil {
		return x.MinutesBefore
	}
	return 0
}

func (x *NotificationSetting) GetSoundType() string {
	if x != nil {
		return x.SoundType
	}
	return ""
}

func (x *NotificationSetting) GetVibration() bool {
	if x != nil {
		return x.Vibration
	}
	return false
}

func (x *NotificationSetting) GetOnlyRamadhan() bool {
	if x != nil {
		return x.OnlyRamadhan
	}
	return false
}

var File_islamic_v1_prayer_proto protoreflect.FileDescriptor

const file_islamic_v1_prayer_proto_rawDesc = "" +
	"\n" +
	"\x17islamic/v1/prayer.proto\x12\n" +
	"islamic.v1\x1a\x11common/base.proto\"\xa7\x02\n" +
	"\x11GetPrayerTimesReq\x12\x1a\n" +
	"\blatitude\x18\x01 \x01(\x01R\blatitude\x12\x1c\n" +
	"\tlongitude\x18\x02 \x01(\x01R\tlongitude\x12\x12\n" +
	"\x04date\x18\x03 \x01(\tR\x04date\x12\x1a\n" +
	"\btimezone\x18\x04 \x01(\tR\btimezone\x12-\n" +
	"\x12calculation_method\x18\x05 \x01(\tR\x11calculationMethod\x12\x1d\n" +
	"\n" +
	"fajr_angle\x18\x06 \x01(\x01R\tfajrAngle\x12\x1d\n" +
	"\n" +
	"isha_angle\x18\a \x01(\x01R\tishaAngle\x12\x1d\n" +
	"\n" +
	"asr_method\x18\b \x01(\tR\tasrMethod\x12\x1c\n" +
	"\televation\x18\t \x01(\x01R\televation\"\x8f\x01\n" +
	"\x11GetPrayerTimesRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12/\n" +
	"\x04data\x18\x04 \x01(\v2\x1b.islamic.v1.PrayerTimesDataR\x04data\"\xca\x02\n" +
	"\x0fPrayerTimesData\x12:\n" +
	"\fprayer_times\x18\x01 \x01(\v2\x17.islamic.v1.PrayerTimesR\vprayerTimes\x124\n" +
	"\blocation\x18\x02 \x01(\v2\x18.islamic.v1.LocationInfoR\blocation\x12:\n" +
	"\fislamic_date\x18\x03 \x01(\v2\x17.islamic.v1.IslamicDateR\vislamicDate\x12\x1f\n" +
	"\vis_ramadhan\x18\x04 \x01(\bR\n" +
	"isRamadhan\x12%\n" +
	"\x0ecurrent_prayer\x18\x05 \x01(\tR\rcurrentPrayer\x12\x1f\n" +
	"\vnext_prayer\x18\x06 \x01(\tR\n" +
	"nextPrayer\x12 \n" +
	"\ftime_to_next\x18\a \x01(\x05R\n" +
	"timeToNext\"\xc1\x01\n" +
	"\vPrayerTimes\x12\x14\n" +
	"\x05imsak\x18\x01 \x01(\tR\x05imsak\x12\x14\n" +
	"\x05subuh\x18\x02 \x01(\tR\x05subuh\x12\x16\n" +
	"\x06terbit\x18\x03 \x01(\tR\x06terbit\x12\x14\n" +
	"\x05dhuha\x18\x04 \x01(\tR\x05dhuha\x12\x14\n" +
	"\x05zuhur\x18\x05 \x01(\tR\x05zuhur\x12\x14\n" +
	"\x05ashar\x18\x06 \x01(\tR\x05ashar\x12\x18\n" +
	"\amaghrib\x18\a \x01(\tR\amaghrib\x12\x12\n" +
	"\x04isya\x18\b \x01(\tR\x04isya\"\xa4\x01\n" +
	"\fLocationInfo\x12\x1a\n" +
	"\blatitude\x18\x01 \x01(\x01R\blatitude\x12\x1c\n" +
	"\tlongitude\x18\x02 \x01(\x01R\tlongitude\x12\x1b\n" +
	"\tcity_name\x18\x03 \x01(\tR\bcityName\x12!\n" +
	"\fcountry_name\x18\x04 \x01(\tR\vcountryName\x12\x1a\n" +
	"\btimezone\x18\x05 \x01(\tR\btimezone\"\x86\x01\n" +
	"\vIslamicDate\x12\x12\n" +
	"\x04year\x18\x01 \x01(\x05R\x04year\x12\x14\n" +
	"\x05month\x18\x02 \x01(\x05R\x05month\x12\x10\n" +
	"\x03day\x18\x03 \x01(\x05R\x03day\x12\x1d\n" +
	"\n" +
	"month_name\x18\x04 \x01(\tR\tmonthName\x12\x1c\n" +
	"\tformatted\x18\x05 \x01(\tR\tformatted\"3\n" +
	"\x18GetUserPrayerSettingsReq\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\"\x99\x01\n" +
	"\x18GetUserPrayerSettingsRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x122\n" +
	"\x04data\x18\x04 \x01(\v2\x1e.islamic.v1.UserPrayerSettingsR\x04data\"\xc5\x02\n" +
	"\x12UserPrayerSettings\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12-\n" +
	"\x12calculation_method\x18\x02 \x01(\tR\x11calculationMethod\x12\x1d\n" +
	"\n" +
	"fajr_angle\x18\x03 \x01(\x01R\tfajrAngle\x12\x1d\n" +
	"\n" +
	"isha_angle\x18\x04 \x01(\x01R\tishaAngle\x12\x1d\n" +
	"\n" +
	"asr_method\x18\x05 \x01(\tR\tasrMethod\x12C\n" +
	"\x10default_location\x18\x06 \x01(\v2\x18.islamic.v1.LocationInfoR\x0fdefaultLocation\x12E\n" +
	"\rnotifications\x18\a \x03(\v2\x1f.islamic.v1.NotificationSettingR\rnotifications\"\xce\x02\n" +
	"\x1bUpdateUserPrayerSettingsReq\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12-\n" +
	"\x12calculation_method\x18\x02 \x01(\tR\x11calculationMethod\x12\x1d\n" +
	"\n" +
	"fajr_angle\x18\x03 \x01(\x01R\tfajrAngle\x12\x1d\n" +
	"\n" +
	"isha_angle\x18\x04 \x01(\x01R\tishaAngle\x12\x1d\n" +
	"\n" +
	"asr_method\x18\x05 \x01(\tR\tasrMethod\x12C\n" +
	"\x10default_location\x18\x06 \x01(\v2\x18.islamic.v1.LocationInfoR\x0fdefaultLocation\x12E\n" +
	"\rnotifications\x18\a \x03(\v2\x1f.islamic.v1.NotificationSettingR\rnotifications\"\x82\x01\n" +
	"\x1bUpdateUserPrayerSettingsRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12\x18\n" +
	"\amessage\x18\x04 \x01(\tR\amessage\"\xd9\x01\n" +
	"\x13NotificationSetting\x12\x1f\n" +
	"\vprayer_name\x18\x01 \x01(\tR\n" +
	"prayerName\x12\x18\n" +
	"\aenabled\x18\x02 \x01(\bR\aenabled\x12%\n" +
	"\x0eminutes_before\x18\x03 \x01(\x05R\rminutesBefore\x12\x1d\n" +
	"\n" +
	"sound_type\x18\x04 \x01(\tR\tsoundType\x12\x1c\n" +
	"\tvibration\x18\x05 \x01(\bR\tvibration\x12#\n" +
	"\ronly_ramadhan\x18\x06 \x01(\bR\fonlyRamadhan2\xb6\x02\n" +
	"\x11PrayerTimeService\x12N\n" +
	"\x0eGetPrayerTimes\x12\x1d.islamic.v1.GetPrayerTimesReq\x1a\x1d.islamic.v1.GetPrayerTimesRes\x12c\n" +
	"\x15GetUserPrayerSettings\x12$.islamic.v1.GetUserPrayerSettingsReq\x1a$.islamic.v1.GetUserPrayerSettingsRes\x12l\n" +
	"\x18UpdateUserPrayerSettings\x12'.islamic.v1.UpdateUserPrayerSettingsReq\x1a'.islamic.v1.UpdateUserPrayerSettingsResB<Z:halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1b\x06proto3"

var (
	file_islamic_v1_prayer_proto_rawDescOnce sync.Once
	file_islamic_v1_prayer_proto_rawDescData []byte
)

func file_islamic_v1_prayer_proto_rawDescGZIP() []byte {
	file_islamic_v1_prayer_proto_rawDescOnce.Do(func() {
		file_islamic_v1_prayer_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_islamic_v1_prayer_proto_rawDesc), len(file_islamic_v1_prayer_proto_rawDesc)))
	})
	return file_islamic_v1_prayer_proto_rawDescData
}

var file_islamic_v1_prayer_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_islamic_v1_prayer_proto_goTypes = []any{
	(*GetPrayerTimesReq)(nil),           // 0: islamic.v1.GetPrayerTimesReq
	(*GetPrayerTimesRes)(nil),           // 1: islamic.v1.GetPrayerTimesRes
	(*PrayerTimesData)(nil),             // 2: islamic.v1.PrayerTimesData
	(*PrayerTimes)(nil),                 // 3: islamic.v1.PrayerTimes
	(*LocationInfo)(nil),                // 4: islamic.v1.LocationInfo
	(*IslamicDate)(nil),                 // 5: islamic.v1.IslamicDate
	(*GetUserPrayerSettingsReq)(nil),    // 6: islamic.v1.GetUserPrayerSettingsReq
	(*GetUserPrayerSettingsRes)(nil),    // 7: islamic.v1.GetUserPrayerSettingsRes
	(*UserPrayerSettings)(nil),          // 8: islamic.v1.UserPrayerSettings
	(*UpdateUserPrayerSettingsReq)(nil), // 9: islamic.v1.UpdateUserPrayerSettingsReq
	(*UpdateUserPrayerSettingsRes)(nil), // 10: islamic.v1.UpdateUserPrayerSettingsRes
	(*NotificationSetting)(nil),         // 11: islamic.v1.NotificationSetting
	(*common.Error)(nil),                // 12: common.Error
}
var file_islamic_v1_prayer_proto_depIdxs = []int32{
	12, // 0: islamic.v1.GetPrayerTimesRes.error:type_name -> common.Error
	2,  // 1: islamic.v1.GetPrayerTimesRes.data:type_name -> islamic.v1.PrayerTimesData
	3,  // 2: islamic.v1.PrayerTimesData.prayer_times:type_name -> islamic.v1.PrayerTimes
	4,  // 3: islamic.v1.PrayerTimesData.location:type_name -> islamic.v1.LocationInfo
	5,  // 4: islamic.v1.PrayerTimesData.islamic_date:type_name -> islamic.v1.IslamicDate
	12, // 5: islamic.v1.GetUserPrayerSettingsRes.error:type_name -> common.Error
	8,  // 6: islamic.v1.GetUserPrayerSettingsRes.data:type_name -> islamic.v1.UserPrayerSettings
	4,  // 7: islamic.v1.UserPrayerSettings.default_location:type_name -> islamic.v1.LocationInfo
	11, // 8: islamic.v1.UserPrayerSettings.notifications:type_name -> islamic.v1.NotificationSetting
	4,  // 9: islamic.v1.UpdateUserPrayerSettingsReq.default_location:type_name -> islamic.v1.LocationInfo
	11, // 10: islamic.v1.UpdateUserPrayerSettingsReq.notifications:type_name -> islamic.v1.NotificationSetting
	12, // 11: islamic.v1.UpdateUserPrayerSettingsRes.error:type_name -> common.Error
	0,  // 12: islamic.v1.PrayerTimeService.GetPrayerTimes:input_type -> islamic.v1.GetPrayerTimesReq
	6,  // 13: islamic.v1.PrayerTimeService.GetUserPrayerSettings:input_type -> islamic.v1.GetUserPrayerSettingsReq
	9,  // 14: islamic.v1.PrayerTimeService.UpdateUserPrayerSettings:input_type -> islamic.v1.UpdateUserPrayerSettingsReq
	1,  // 15: islamic.v1.PrayerTimeService.GetPrayerTimes:output_type -> islamic.v1.GetPrayerTimesRes
	7,  // 16: islamic.v1.PrayerTimeService.GetUserPrayerSettings:output_type -> islamic.v1.GetUserPrayerSettingsRes
	10, // 17: islamic.v1.PrayerTimeService.UpdateUserPrayerSettings:output_type -> islamic.v1.UpdateUserPrayerSettingsRes
	15, // [15:18] is the sub-list for method output_type
	12, // [12:15] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_islamic_v1_prayer_proto_init() }
func file_islamic_v1_prayer_proto_init() {
	if File_islamic_v1_prayer_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_islamic_v1_prayer_proto_rawDesc), len(file_islamic_v1_prayer_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_islamic_v1_prayer_proto_goTypes,
		DependencyIndexes: file_islamic_v1_prayer_proto_depIdxs,
		MessageInfos:      file_islamic_v1_prayer_proto_msgTypes,
	}.Build()
	File_islamic_v1_prayer_proto = out.File
	file_islamic_v1_prayer_proto_goTypes = nil
	file_islamic_v1_prayer_proto_depIdxs = nil
}
