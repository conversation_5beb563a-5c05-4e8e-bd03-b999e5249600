// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: islamic/v1/prayer.proto

package islamicv1

import (
	common "halalplus/api/common"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 祷告时间查询请求
type GetPrayerTimesReq struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Latitude       float64                `protobuf:"fixed64,1,opt,name=latitude,proto3" json:"latitude,omitempty" dc:"纬度"`                                                                     // 纬度
	Longitude      float64                `protobuf:"fixed64,2,opt,name=longitude,proto3" json:"longitude,omitempty" dc:"经度"`                                                                   // 经度
	Timezone       string                 `protobuf:"bytes,3,opt,name=timezone,proto3" json:"timezone,omitempty" dc:"时区，如 'Asia/Shanghai'"`                                                     // 时区，如 "Asia/Shanghai"
	MethodCode     string                 `protobuf:"bytes,4,opt,name=method_code,json=methodCode,proto3" json:"method_code,omitempty" dc:"计算方法：AUTO, LFNU, UMMUL_QURA (这个设置是在日历那边，用于返回伊斯兰日期)"` // 计算方法：AUTO, LFNU, UMMUL_QURA (这个设置是在日历那边，用于返回伊斯兰日期)
	DateAdjustment int32                  `protobuf:"varint,5,opt,name=date_adjustment,json=dateAdjustment,proto3" json:"date_adjustment,omitempty" dc:"日期校正：-3到+3天的偏移量 (这个设置是在日历那边)"`          // 日期校正：-3到+3天的偏移量 (这个设置是在日历那边)
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetPrayerTimesReq) Reset() {
	*x = GetPrayerTimesReq{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPrayerTimesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPrayerTimesReq) ProtoMessage() {}

func (x *GetPrayerTimesReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPrayerTimesReq.ProtoReflect.Descriptor instead.
func (*GetPrayerTimesReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{0}
}

func (x *GetPrayerTimesReq) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *GetPrayerTimesReq) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *GetPrayerTimesReq) GetTimezone() string {
	if x != nil {
		return x.Timezone
	}
	return ""
}

func (x *GetPrayerTimesReq) GetMethodCode() string {
	if x != nil {
		return x.MethodCode
	}
	return ""
}

func (x *GetPrayerTimesReq) GetDateAdjustment() int32 {
	if x != nil {
		return x.DateAdjustment
	}
	return 0
}

// 祷告时间查询响应
type GetPrayerTimesRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *PrayerTimesData       `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPrayerTimesRes) Reset() {
	*x = GetPrayerTimesRes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPrayerTimesRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPrayerTimesRes) ProtoMessage() {}

func (x *GetPrayerTimesRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPrayerTimesRes.ProtoReflect.Descriptor instead.
func (*GetPrayerTimesRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{1}
}

func (x *GetPrayerTimesRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetPrayerTimesRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetPrayerTimesRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *GetPrayerTimesRes) GetData() *PrayerTimesData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 祷告时间数据
type PrayerTimesData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PrayerTimes   *PrayerTimes           `protobuf:"bytes,1,opt,name=prayer_times,json=prayerTimes,proto3" json:"prayer_times,omitempty" dc:"祷告时间"`   // 祷告时间
	IslamicDate   *IslamicDate           `protobuf:"bytes,2,opt,name=islamic_date,json=islamicDate,proto3" json:"islamic_date,omitempty" dc:"伊斯兰历日期"` // 伊斯兰历日期
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PrayerTimesData) Reset() {
	*x = PrayerTimesData{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PrayerTimesData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrayerTimesData) ProtoMessage() {}

func (x *PrayerTimesData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrayerTimesData.ProtoReflect.Descriptor instead.
func (*PrayerTimesData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{2}
}

func (x *PrayerTimesData) GetPrayerTimes() *PrayerTimes {
	if x != nil {
		return x.PrayerTimes
	}
	return nil
}

func (x *PrayerTimesData) GetIslamicDate() *IslamicDate {
	if x != nil {
		return x.IslamicDate
	}
	return nil
}

// 祷告时间
type PrayerTimes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Imsak         string                 `protobuf:"bytes,1,opt,name=imsak,proto3" json:"imsak,omitempty" dc:"伊姆萨克时间（仅斋月期间）"`     // 伊姆萨克时间（仅斋月期间）
	Subuh         string                 `protobuf:"bytes,2,opt,name=subuh,proto3" json:"subuh,omitempty" dc:"晨祷时间"`              // 晨祷时间
	Terbit        string                 `protobuf:"bytes,3,opt,name=terbit,proto3" json:"terbit,omitempty" dc:"日出时间"`            // 日出时间
	Dhuha         string                 `protobuf:"bytes,4,opt,name=dhuha,proto3" json:"dhuha,omitempty" dc:"上午祷告时间（可选，目前还不准确）"` // 上午祷告时间（可选，目前还不准确）
	Zuhur         string                 `protobuf:"bytes,5,opt,name=zuhur,proto3" json:"zuhur,omitempty" dc:"晌祷时间"`              // 晌祷时间
	Ashar         string                 `protobuf:"bytes,6,opt,name=ashar,proto3" json:"ashar,omitempty" dc:"晡祷时间"`              // 晡祷时间
	Maghrib       string                 `protobuf:"bytes,7,opt,name=maghrib,proto3" json:"maghrib,omitempty" dc:"昏祷时间"`          // 昏祷时间
	Isya          string                 `protobuf:"bytes,8,opt,name=isya,proto3" json:"isya,omitempty" dc:"宵祷时间"`                // 宵祷时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PrayerTimes) Reset() {
	*x = PrayerTimes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PrayerTimes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrayerTimes) ProtoMessage() {}

func (x *PrayerTimes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrayerTimes.ProtoReflect.Descriptor instead.
func (*PrayerTimes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{3}
}

func (x *PrayerTimes) GetImsak() string {
	if x != nil {
		return x.Imsak
	}
	return ""
}

func (x *PrayerTimes) GetSubuh() string {
	if x != nil {
		return x.Subuh
	}
	return ""
}

func (x *PrayerTimes) GetTerbit() string {
	if x != nil {
		return x.Terbit
	}
	return ""
}

func (x *PrayerTimes) GetDhuha() string {
	if x != nil {
		return x.Dhuha
	}
	return ""
}

func (x *PrayerTimes) GetZuhur() string {
	if x != nil {
		return x.Zuhur
	}
	return ""
}

func (x *PrayerTimes) GetAshar() string {
	if x != nil {
		return x.Ashar
	}
	return ""
}

func (x *PrayerTimes) GetMaghrib() string {
	if x != nil {
		return x.Maghrib
	}
	return ""
}

func (x *PrayerTimes) GetIsya() string {
	if x != nil {
		return x.Isya
	}
	return ""
}

// 伊斯兰历日期
type IslamicDate struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Year          int32                  `protobuf:"varint,1,opt,name=year,proto3" json:"year,omitempty" dc:"伊斯兰历年份"`   // 伊斯兰历年份
	Month         int32                  `protobuf:"varint,2,opt,name=month,proto3" json:"month,omitempty" dc:"伊斯兰历月份"` // 伊斯兰历月份
	Day           int32                  `protobuf:"varint,3,opt,name=day,proto3" json:"day,omitempty" dc:"伊斯兰历日期"`     // 伊斯兰历日期
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IslamicDate) Reset() {
	*x = IslamicDate{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IslamicDate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IslamicDate) ProtoMessage() {}

func (x *IslamicDate) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IslamicDate.ProtoReflect.Descriptor instead.
func (*IslamicDate) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{4}
}

func (x *IslamicDate) GetYear() int32 {
	if x != nil {
		return x.Year
	}
	return 0
}

func (x *IslamicDate) GetMonth() int32 {
	if x != nil {
		return x.Month
	}
	return 0
}

func (x *IslamicDate) GetDay() int32 {
	if x != nil {
		return x.Day
	}
	return 0
}

var File_islamic_v1_prayer_proto protoreflect.FileDescriptor

const file_islamic_v1_prayer_proto_rawDesc = "" +
	"\n" +
	"\x17islamic/v1/prayer.proto\x12\n" +
	"islamic.v1\x1a\x11common/base.proto\"\xb3\x01\n" +
	"\x11GetPrayerTimesReq\x12\x1a\n" +
	"\blatitude\x18\x01 \x01(\x01R\blatitude\x12\x1c\n" +
	"\tlongitude\x18\x02 \x01(\x01R\tlongitude\x12\x1a\n" +
	"\btimezone\x18\x03 \x01(\tR\btimezone\x12\x1f\n" +
	"\vmethod_code\x18\x04 \x01(\tR\n" +
	"methodCode\x12'\n" +
	"\x0fdate_adjustment\x18\x05 \x01(\x05R\x0edateAdjustment\"\x8f\x01\n" +
	"\x11GetPrayerTimesRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12/\n" +
	"\x04data\x18\x04 \x01(\v2\x1b.islamic.v1.PrayerTimesDataR\x04data\"\x89\x01\n" +
	"\x0fPrayerTimesData\x12:\n" +
	"\fprayer_times\x18\x01 \x01(\v2\x17.islamic.v1.PrayerTimesR\vprayerTimes\x12:\n" +
	"\fislamic_date\x18\x02 \x01(\v2\x17.islamic.v1.IslamicDateR\vislamicDate\"\xc1\x01\n" +
	"\vPrayerTimes\x12\x14\n" +
	"\x05imsak\x18\x01 \x01(\tR\x05imsak\x12\x14\n" +
	"\x05subuh\x18\x02 \x01(\tR\x05subuh\x12\x16\n" +
	"\x06terbit\x18\x03 \x01(\tR\x06terbit\x12\x14\n" +
	"\x05dhuha\x18\x04 \x01(\tR\x05dhuha\x12\x14\n" +
	"\x05zuhur\x18\x05 \x01(\tR\x05zuhur\x12\x14\n" +
	"\x05ashar\x18\x06 \x01(\tR\x05ashar\x12\x18\n" +
	"\amaghrib\x18\a \x01(\tR\amaghrib\x12\x12\n" +
	"\x04isya\x18\b \x01(\tR\x04isya\"I\n" +
	"\vIslamicDate\x12\x12\n" +
	"\x04year\x18\x01 \x01(\x05R\x04year\x12\x14\n" +
	"\x05month\x18\x02 \x01(\x05R\x05month\x12\x10\n" +
	"\x03day\x18\x03 \x01(\x05R\x03day2c\n" +
	"\x11PrayerTimeService\x12N\n" +
	"\x0eGetPrayerTimes\x12\x1d.islamic.v1.GetPrayerTimesReq\x1a\x1d.islamic.v1.GetPrayerTimesResB<Z:halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1b\x06proto3"

var (
	file_islamic_v1_prayer_proto_rawDescOnce sync.Once
	file_islamic_v1_prayer_proto_rawDescData []byte
)

func file_islamic_v1_prayer_proto_rawDescGZIP() []byte {
	file_islamic_v1_prayer_proto_rawDescOnce.Do(func() {
		file_islamic_v1_prayer_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_islamic_v1_prayer_proto_rawDesc), len(file_islamic_v1_prayer_proto_rawDesc)))
	})
	return file_islamic_v1_prayer_proto_rawDescData
}

var file_islamic_v1_prayer_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_islamic_v1_prayer_proto_goTypes = []any{
	(*GetPrayerTimesReq)(nil), // 0: islamic.v1.GetPrayerTimesReq
	(*GetPrayerTimesRes)(nil), // 1: islamic.v1.GetPrayerTimesRes
	(*PrayerTimesData)(nil),   // 2: islamic.v1.PrayerTimesData
	(*PrayerTimes)(nil),       // 3: islamic.v1.PrayerTimes
	(*IslamicDate)(nil),       // 4: islamic.v1.IslamicDate
	(*common.Error)(nil),      // 5: common.Error
}
var file_islamic_v1_prayer_proto_depIdxs = []int32{
	5, // 0: islamic.v1.GetPrayerTimesRes.error:type_name -> common.Error
	2, // 1: islamic.v1.GetPrayerTimesRes.data:type_name -> islamic.v1.PrayerTimesData
	3, // 2: islamic.v1.PrayerTimesData.prayer_times:type_name -> islamic.v1.PrayerTimes
	4, // 3: islamic.v1.PrayerTimesData.islamic_date:type_name -> islamic.v1.IslamicDate
	0, // 4: islamic.v1.PrayerTimeService.GetPrayerTimes:input_type -> islamic.v1.GetPrayerTimesReq
	1, // 5: islamic.v1.PrayerTimeService.GetPrayerTimes:output_type -> islamic.v1.GetPrayerTimesRes
	5, // [5:6] is the sub-list for method output_type
	4, // [4:5] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_islamic_v1_prayer_proto_init() }
func file_islamic_v1_prayer_proto_init() {
	if File_islamic_v1_prayer_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_islamic_v1_prayer_proto_rawDesc), len(file_islamic_v1_prayer_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_islamic_v1_prayer_proto_goTypes,
		DependencyIndexes: file_islamic_v1_prayer_proto_depIdxs,
		MessageInfos:      file_islamic_v1_prayer_proto_msgTypes,
	}.Build()
	File_islamic_v1_prayer_proto = out.File
	file_islamic_v1_prayer_proto_goTypes = nil
	file_islamic_v1_prayer_proto_depIdxs = nil
}
