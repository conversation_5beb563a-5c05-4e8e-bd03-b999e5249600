// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.31.1
// source: islamic/v1/prayer.proto

package islamicv1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	PrayerTimeService_GetPrayerTimes_FullMethodName           = "/islamic.v1.PrayerTimeService/GetPrayerTimes"
	PrayerTimeService_GetUserPrayerSettings_FullMethodName    = "/islamic.v1.PrayerTimeService/GetUserPrayerSettings"
	PrayerTimeService_UpdateUserPrayerSettings_FullMethodName = "/islamic.v1.PrayerTimeService/UpdateUserPrayerSettings"
)

// PrayerTimeServiceClient is the client API for PrayerTimeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 祷告时间服务定义
type PrayerTimeServiceClient interface {
	// 获取祷告时间
	GetPrayerTimes(ctx context.Context, in *GetPrayerTimesReq, opts ...grpc.CallOption) (*GetPrayerTimesRes, error)
	// 获取用户祷告设置
	GetUserPrayerSettings(ctx context.Context, in *GetUserPrayerSettingsReq, opts ...grpc.CallOption) (*GetUserPrayerSettingsRes, error)
	// 更新用户祷告设置
	UpdateUserPrayerSettings(ctx context.Context, in *UpdateUserPrayerSettingsReq, opts ...grpc.CallOption) (*UpdateUserPrayerSettingsRes, error)
}

type prayerTimeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPrayerTimeServiceClient(cc grpc.ClientConnInterface) PrayerTimeServiceClient {
	return &prayerTimeServiceClient{cc}
}

func (c *prayerTimeServiceClient) GetPrayerTimes(ctx context.Context, in *GetPrayerTimesReq, opts ...grpc.CallOption) (*GetPrayerTimesRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPrayerTimesRes)
	err := c.cc.Invoke(ctx, PrayerTimeService_GetPrayerTimes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerTimeServiceClient) GetUserPrayerSettings(ctx context.Context, in *GetUserPrayerSettingsReq, opts ...grpc.CallOption) (*GetUserPrayerSettingsRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserPrayerSettingsRes)
	err := c.cc.Invoke(ctx, PrayerTimeService_GetUserPrayerSettings_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerTimeServiceClient) UpdateUserPrayerSettings(ctx context.Context, in *UpdateUserPrayerSettingsReq, opts ...grpc.CallOption) (*UpdateUserPrayerSettingsRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateUserPrayerSettingsRes)
	err := c.cc.Invoke(ctx, PrayerTimeService_UpdateUserPrayerSettings_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PrayerTimeServiceServer is the server API for PrayerTimeService service.
// All implementations must embed UnimplementedPrayerTimeServiceServer
// for forward compatibility.
//
// 祷告时间服务定义
type PrayerTimeServiceServer interface {
	// 获取祷告时间
	GetPrayerTimes(context.Context, *GetPrayerTimesReq) (*GetPrayerTimesRes, error)
	// 获取用户祷告设置
	GetUserPrayerSettings(context.Context, *GetUserPrayerSettingsReq) (*GetUserPrayerSettingsRes, error)
	// 更新用户祷告设置
	UpdateUserPrayerSettings(context.Context, *UpdateUserPrayerSettingsReq) (*UpdateUserPrayerSettingsRes, error)
	mustEmbedUnimplementedPrayerTimeServiceServer()
}

// UnimplementedPrayerTimeServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPrayerTimeServiceServer struct{}

func (UnimplementedPrayerTimeServiceServer) GetPrayerTimes(context.Context, *GetPrayerTimesReq) (*GetPrayerTimesRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPrayerTimes not implemented")
}
func (UnimplementedPrayerTimeServiceServer) GetUserPrayerSettings(context.Context, *GetUserPrayerSettingsReq) (*GetUserPrayerSettingsRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserPrayerSettings not implemented")
}
func (UnimplementedPrayerTimeServiceServer) UpdateUserPrayerSettings(context.Context, *UpdateUserPrayerSettingsReq) (*UpdateUserPrayerSettingsRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserPrayerSettings not implemented")
}
func (UnimplementedPrayerTimeServiceServer) mustEmbedUnimplementedPrayerTimeServiceServer() {}
func (UnimplementedPrayerTimeServiceServer) testEmbeddedByValue()                           {}

// UnsafePrayerTimeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PrayerTimeServiceServer will
// result in compilation errors.
type UnsafePrayerTimeServiceServer interface {
	mustEmbedUnimplementedPrayerTimeServiceServer()
}

func RegisterPrayerTimeServiceServer(s grpc.ServiceRegistrar, srv PrayerTimeServiceServer) {
	// If the following call pancis, it indicates UnimplementedPrayerTimeServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PrayerTimeService_ServiceDesc, srv)
}

func _PrayerTimeService_GetPrayerTimes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPrayerTimesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerTimeServiceServer).GetPrayerTimes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrayerTimeService_GetPrayerTimes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerTimeServiceServer).GetPrayerTimes(ctx, req.(*GetPrayerTimesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerTimeService_GetUserPrayerSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPrayerSettingsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerTimeServiceServer).GetUserPrayerSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrayerTimeService_GetUserPrayerSettings_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerTimeServiceServer).GetUserPrayerSettings(ctx, req.(*GetUserPrayerSettingsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerTimeService_UpdateUserPrayerSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserPrayerSettingsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerTimeServiceServer).UpdateUserPrayerSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrayerTimeService_UpdateUserPrayerSettings_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerTimeServiceServer).UpdateUserPrayerSettings(ctx, req.(*UpdateUserPrayerSettingsReq))
	}
	return interceptor(ctx, in, info, handler)
}

// PrayerTimeService_ServiceDesc is the grpc.ServiceDesc for PrayerTimeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PrayerTimeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "islamic.v1.PrayerTimeService",
	HandlerType: (*PrayerTimeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPrayerTimes",
			Handler:    _PrayerTimeService_GetPrayerTimes_Handler,
		},
		{
			MethodName: "GetUserPrayerSettings",
			Handler:    _PrayerTimeService_GetUserPrayerSettings_Handler,
		},
		{
			MethodName: "UpdateUserPrayerSettings",
			Handler:    _PrayerTimeService_UpdateUserPrayerSettings_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "islamic/v1/prayer.proto",
}
