// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: islamic/v1/calendar.proto

package islamicv1

import (
	common "halalplus/api/common"
	_ "halalplus/app/islamic-content-svc/api/pbentity"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 日历查询请求
type CalendarReq struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Year           int32                  `protobuf:"varint,1,opt,name=year,proto3" json:"year,omitempty" dc:"公历年份"`                                                      // 公历年份
	Month          int32                  `protobuf:"varint,2,opt,name=month,proto3" json:"month,omitempty" dc:"公历月份"`                                                    // 公历月份
	MethodCode     string                 `protobuf:"bytes,3,opt,name=method_code,json=methodCode,proto3" json:"method_code,omitempty" dc:"计算方法：AUTO, LFNU, UMMUL_QURA"`  // 计算方法：AUTO, LFNU, UMMUL_QURA
	DateAdjustment int32                  `protobuf:"varint,4,opt,name=date_adjustment,json=dateAdjustment,proto3" json:"date_adjustment,omitempty" dc:"日期校正：-3到+3天的偏移量"` // 日期校正：-3到+3天的偏移量
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CalendarReq) Reset() {
	*x = CalendarReq{}
	mi := &file_islamic_v1_calendar_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CalendarReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalendarReq) ProtoMessage() {}

func (x *CalendarReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_calendar_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalendarReq.ProtoReflect.Descriptor instead.
func (*CalendarReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_calendar_proto_rawDescGZIP(), []int{0}
}

func (x *CalendarReq) GetYear() int32 {
	if x != nil {
		return x.Year
	}
	return 0
}

func (x *CalendarReq) GetMonth() int32 {
	if x != nil {
		return x.Month
	}
	return 0
}

func (x *CalendarReq) GetMethodCode() string {
	if x != nil {
		return x.MethodCode
	}
	return ""
}

func (x *CalendarReq) GetDateAdjustment() int32 {
	if x != nil {
		return x.DateAdjustment
	}
	return 0
}

// 日历日期信息
type CalendarDateInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	GregorianYear  int32                  `protobuf:"varint,1,opt,name=gregorian_year,json=gregorianYear,proto3" json:"gregorian_year,omitempty" dc:"公历年"`                                                                                                                                            // 公历年
	GregorianMonth int32                  `protobuf:"varint,2,opt,name=gregorian_month,json=gregorianMonth,proto3" json:"gregorian_month,omitempty" dc:"公历月"`                                                                                                                                         // 公历月
	GregorianDay   int32                  `protobuf:"varint,3,opt,name=gregorian_day,json=gregorianDay,proto3" json:"gregorian_day,omitempty" dc:"公历日"`                                                                                                                                               // 公历日
	HijriahYear    int32                  `protobuf:"varint,4,opt,name=hijriah_year,json=hijriahYear,proto3" json:"hijriah_year,omitempty" dc:"Hijriah年"`                                                                                                                                             // Hijriah年
	HijriahMonth   int32                  `protobuf:"varint,5,opt,name=hijriah_month,json=hijriahMonth,proto3" json:"hijriah_month,omitempty" dc:"Hijriah月 1-12 (Muharam, Safar, Rabiul Awal, Rabiul Akhir, Jumadal Ula, Jumadal Akhirah, Rajab, Sya'ban, Ramadhan, Syawal, Dzulqa'dah, Dzulhijjah)"` // Hijriah月 1-12 (Muharam, Safar, Rabiul Awal, Rabiul Akhir, Jumadal Ula, Jumadal Akhirah, Rajab, Sya'ban, Ramadhan, Syawal, Dzulqa'dah, Dzulhijjah)
	HijriahDay     int32                  `protobuf:"varint,6,opt,name=hijriah_day,json=hijriahDay,proto3" json:"hijriah_day,omitempty" dc:"Hijriah日"`                                                                                                                                                // Hijriah日
	MethodCode     string                 `protobuf:"bytes,7,opt,name=method_code,json=methodCode,proto3" json:"method_code,omitempty" dc:"计算方法代码"`                                                                                                                                                   // 计算方法代码
	Weekday        int32                  `protobuf:"varint,8,opt,name=weekday,proto3" json:"weekday,omitempty" dc:"星期：0-6 (0=Sunday, 1=Monday, ..., 6=Saturday)"`                                                                                                                                    // 星期：0-6 (0=Sunday, 1=Monday, ..., 6=Saturday)
	Pasaran        int32                  `protobuf:"varint,9,opt,name=pasaran,proto3" json:"pasaran,omitempty" dc:"Pasaran：0-4 (0=Kliwon, 1=Legi, 2=Pahing, 3=Pon, 4=Wage)"`                                                                                                                         // Pasaran：0-4 (0=Kliwon, 1=Legi, 2=Pahing, 3=Pon, 4=Wage)
	WeekdayName    string                 `protobuf:"bytes,10,opt,name=weekday_name,json=weekdayName,proto3" json:"weekday_name,omitempty" dc:"星期名称（本地化）(Ahad Senin Selasa Rabu Kamis Jumat Sabtu)"`                                                                                                  // 星期名称（本地化）(Ahad Senin Selasa Rabu Kamis Jumat Sabtu)
	PasaranName    string                 `protobuf:"bytes,11,opt,name=pasaran_name,json=pasaranName,proto3" json:"pasaran_name,omitempty" dc:"Pasaran名称（本地化）"`                                                                                                                                       // Pasaran名称（本地化）
	Events         []*CalendarEventInfo   `protobuf:"bytes,12,rep,name=events,proto3" json:"events,omitempty" dc:"当日事件列表"`                                                                                                                                                                            // 当日事件列表
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CalendarDateInfo) Reset() {
	*x = CalendarDateInfo{}
	mi := &file_islamic_v1_calendar_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CalendarDateInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalendarDateInfo) ProtoMessage() {}

func (x *CalendarDateInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_calendar_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalendarDateInfo.ProtoReflect.Descriptor instead.
func (*CalendarDateInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_calendar_proto_rawDescGZIP(), []int{1}
}

func (x *CalendarDateInfo) GetGregorianYear() int32 {
	if x != nil {
		return x.GregorianYear
	}
	return 0
}

func (x *CalendarDateInfo) GetGregorianMonth() int32 {
	if x != nil {
		return x.GregorianMonth
	}
	return 0
}

func (x *CalendarDateInfo) GetGregorianDay() int32 {
	if x != nil {
		return x.GregorianDay
	}
	return 0
}

func (x *CalendarDateInfo) GetHijriahYear() int32 {
	if x != nil {
		return x.HijriahYear
	}
	return 0
}

func (x *CalendarDateInfo) GetHijriahMonth() int32 {
	if x != nil {
		return x.HijriahMonth
	}
	return 0
}

func (x *CalendarDateInfo) GetHijriahDay() int32 {
	if x != nil {
		return x.HijriahDay
	}
	return 0
}

func (x *CalendarDateInfo) GetMethodCode() string {
	if x != nil {
		return x.MethodCode
	}
	return ""
}

func (x *CalendarDateInfo) GetWeekday() int32 {
	if x != nil {
		return x.Weekday
	}
	return 0
}

func (x *CalendarDateInfo) GetPasaran() int32 {
	if x != nil {
		return x.Pasaran
	}
	return 0
}

func (x *CalendarDateInfo) GetWeekdayName() string {
	if x != nil {
		return x.WeekdayName
	}
	return ""
}

func (x *CalendarDateInfo) GetPasaranName() string {
	if x != nil {
		return x.PasaranName
	}
	return ""
}

func (x *CalendarDateInfo) GetEvents() []*CalendarEventInfo {
	if x != nil {
		return x.Events
	}
	return nil
}

// 日历事件信息
type CalendarEventInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"事件ID"`                                                                 // 事件ID
	EventType     string                 `protobuf:"bytes,2,opt,name=event_type,json=eventType,proto3" json:"event_type,omitempty" dc:"事件类型：HARI_BESAR, LIBUR_NASIONAL, PUASA"` // 事件类型：HARI_BESAR, LIBUR_NASIONAL, PUASA
	Title         string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty" dc:"事件标题"`                                                            // 事件标题
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty" dc:"事件描述"`                                                // 事件描述
	JumpUrl       string                 `protobuf:"bytes,5,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty" dc:"点击跳转链接"`                                       // 点击跳转链接
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CalendarEventInfo) Reset() {
	*x = CalendarEventInfo{}
	mi := &file_islamic_v1_calendar_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CalendarEventInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalendarEventInfo) ProtoMessage() {}

func (x *CalendarEventInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_calendar_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalendarEventInfo.ProtoReflect.Descriptor instead.
func (*CalendarEventInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_calendar_proto_rawDescGZIP(), []int{2}
}

func (x *CalendarEventInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CalendarEventInfo) GetEventType() string {
	if x != nil {
		return x.EventType
	}
	return ""
}

func (x *CalendarEventInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CalendarEventInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CalendarEventInfo) GetJumpUrl() string {
	if x != nil {
		return x.JumpUrl
	}
	return ""
}

// 日历响应
type CalendarRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          []*CalendarDateInfo    `protobuf:"bytes,4,rep,name=data,proto3" json:"data,omitempty" dc:"日历数据列表"` // 日历数据列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CalendarRes) Reset() {
	*x = CalendarRes{}
	mi := &file_islamic_v1_calendar_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CalendarRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalendarRes) ProtoMessage() {}

func (x *CalendarRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_calendar_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalendarRes.ProtoReflect.Descriptor instead.
func (*CalendarRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_calendar_proto_rawDescGZIP(), []int{3}
}

func (x *CalendarRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CalendarRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CalendarRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CalendarRes) GetData() []*CalendarDateInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_islamic_v1_calendar_proto protoreflect.FileDescriptor

const file_islamic_v1_calendar_proto_rawDesc = "" +
	"\n" +
	"\x19islamic/v1/calendar.proto\x12\n" +
	"islamic.v1\x1a\x11common/base.proto\x1a\x1fpbentity/calendar_hijriah.proto\x1a\x1epbentity/calendar_events.proto\"\x81\x01\n" +
	"\vCalendarReq\x12\x12\n" +
	"\x04year\x18\x01 \x01(\x05R\x04year\x12\x14\n" +
	"\x05month\x18\x02 \x01(\x05R\x05month\x12\x1f\n" +
	"\vmethod_code\x18\x03 \x01(\tR\n" +
	"methodCode\x12'\n" +
	"\x0fdate_adjustment\x18\x04 \x01(\x05R\x0edateAdjustment\"\xc2\x03\n" +
	"\x10CalendarDateInfo\x12%\n" +
	"\x0egregorian_year\x18\x01 \x01(\x05R\rgregorianYear\x12'\n" +
	"\x0fgregorian_month\x18\x02 \x01(\x05R\x0egregorianMonth\x12#\n" +
	"\rgregorian_day\x18\x03 \x01(\x05R\fgregorianDay\x12!\n" +
	"\fhijriah_year\x18\x04 \x01(\x05R\vhijriahYear\x12#\n" +
	"\rhijriah_month\x18\x05 \x01(\x05R\fhijriahMonth\x12\x1f\n" +
	"\vhijriah_day\x18\x06 \x01(\x05R\n" +
	"hijriahDay\x12\x1f\n" +
	"\vmethod_code\x18\a \x01(\tR\n" +
	"methodCode\x12\x18\n" +
	"\aweekday\x18\b \x01(\x05R\aweekday\x12\x18\n" +
	"\apasaran\x18\t \x01(\x05R\apasaran\x12!\n" +
	"\fweekday_name\x18\n" +
	" \x01(\tR\vweekdayName\x12!\n" +
	"\fpasaran_name\x18\v \x01(\tR\vpasaranName\x125\n" +
	"\x06events\x18\f \x03(\v2\x1d.islamic.v1.CalendarEventInfoR\x06events\"\x95\x01\n" +
	"\x11CalendarEventInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1d\n" +
	"\n" +
	"event_type\x18\x02 \x01(\tR\teventType\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12\x19\n" +
	"\bjump_url\x18\x05 \x01(\tR\ajumpUrl\"\x8a\x01\n" +
	"\vCalendarRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x120\n" +
	"\x04data\x18\x04 \x03(\v2\x1c.islamic.v1.CalendarDateInfoR\x04data2R\n" +
	"\x0fCalendarService\x12?\n" +
	"\vGetCalendar\x12\x17.islamic.v1.CalendarReq\x1a\x17.islamic.v1.CalendarResB<Z:halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1b\x06proto3"

var (
	file_islamic_v1_calendar_proto_rawDescOnce sync.Once
	file_islamic_v1_calendar_proto_rawDescData []byte
)

func file_islamic_v1_calendar_proto_rawDescGZIP() []byte {
	file_islamic_v1_calendar_proto_rawDescOnce.Do(func() {
		file_islamic_v1_calendar_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_islamic_v1_calendar_proto_rawDesc), len(file_islamic_v1_calendar_proto_rawDesc)))
	})
	return file_islamic_v1_calendar_proto_rawDescData
}

var file_islamic_v1_calendar_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_islamic_v1_calendar_proto_goTypes = []any{
	(*CalendarReq)(nil),       // 0: islamic.v1.CalendarReq
	(*CalendarDateInfo)(nil),  // 1: islamic.v1.CalendarDateInfo
	(*CalendarEventInfo)(nil), // 2: islamic.v1.CalendarEventInfo
	(*CalendarRes)(nil),       // 3: islamic.v1.CalendarRes
	(*common.Error)(nil),      // 4: common.Error
}
var file_islamic_v1_calendar_proto_depIdxs = []int32{
	2, // 0: islamic.v1.CalendarDateInfo.events:type_name -> islamic.v1.CalendarEventInfo
	4, // 1: islamic.v1.CalendarRes.error:type_name -> common.Error
	1, // 2: islamic.v1.CalendarRes.data:type_name -> islamic.v1.CalendarDateInfo
	0, // 3: islamic.v1.CalendarService.GetCalendar:input_type -> islamic.v1.CalendarReq
	3, // 4: islamic.v1.CalendarService.GetCalendar:output_type -> islamic.v1.CalendarRes
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_islamic_v1_calendar_proto_init() }
func file_islamic_v1_calendar_proto_init() {
	if File_islamic_v1_calendar_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_islamic_v1_calendar_proto_rawDesc), len(file_islamic_v1_calendar_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_islamic_v1_calendar_proto_goTypes,
		DependencyIndexes: file_islamic_v1_calendar_proto_depIdxs,
		MessageInfos:      file_islamic_v1_calendar_proto_msgTypes,
	}.Build()
	File_islamic_v1_calendar_proto = out.File
	file_islamic_v1_calendar_proto_goTypes = nil
	file_islamic_v1_calendar_proto_depIdxs = nil
}
