package islamic

import (
	"context"
	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/service"

	"github.com/gogf/gf/v2/frame/g"
)

type ControllerPrayer struct {
	v1.UnimplementedPrayerServiceServer
}

// GetPrayerTimes 获取祷告时间
func (*ControllerPrayer) GetPrayerTimes(ctx context.Context, req *v1.GetPrayerTimesReq) (res *v1.GetPrayerTimesRes, err error) {
	// 构建输入参数
	input := &model.PrayerTimeInput{
		Date:              req.Date,
		Latitude:          req.Latitude,
		Longitude:         req.Longitude,
		Timezone:          req.Timezone,
		CalculationMethod: req.CalculationMethod,
		FajrAngle:         req.FajrAngle,
		IshaAngle:         req.<PERSON>ngle,
		AsrMethod:         req.Asr<PERSON>ethod,
		Elevation:         req.Elevation,
	}

	// 调用服务层
	output, err := service.PrayerTime().GetPrayerTimes(ctx, input)
	if err != nil {
		g.Log().Error(ctx, "GetPrayerTimes error:", err)
		return &v1.GetPrayerTimesRes{
			Code: 500,
			Msg:  "Internal server error",
		}, nil
	}

	// 转换祷告时间
	prayerTimes := &v1.PrayerTimes{
		Subuh:   output.PrayerTimes.Subuh,
		Terbit:  output.PrayerTimes.Terbit,
		Dhuha:   output.PrayerTimes.Dhuha,
		Zuhur:   output.PrayerTimes.Zuhur,
		Ashar:   output.PrayerTimes.Ashar,
		Maghrib: output.PrayerTimes.Maghrib,
		Isya:    output.PrayerTimes.Isya,
		Imsak:   output.PrayerTimes.Imsak,
	}

	// 转换位置信息
	var location *v1.LocationInfo
	if output.Location != nil {
		location = &v1.LocationInfo{
			Latitude:    output.Location.Latitude,
			Longitude:   output.Location.Longitude,
			CityName:    output.Location.CityName,
			CountryName: output.Location.CountryName,
			Timezone:    output.Location.Timezone,
		}
	}

	// 转换伊斯兰历信息
	var islamicDate *v1.IslamicDate
	if output.IslamicDate != nil {
		islamicDate = &v1.IslamicDate{
			Year:      output.IslamicDate.Year,
			Month:     output.IslamicDate.Month,
			Day:       output.IslamicDate.Day,
			MonthName: output.IslamicDate.MonthName,
			Formatted: output.IslamicDate.Formatted,
		}
	}

	return &v1.GetPrayerTimesRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.PrayerTimesData{
			PrayerTimes:   prayerTimes,
			Location:      location,
			IslamicDate:   islamicDate,
			IsRamadhan:    output.IsRamadhan,
			CurrentPrayer: output.CurrentPrayer,
			NextPrayer:    output.NextPrayer,
			TimeToNext:    output.TimeToNext,
		},
	}, nil
}

// GetUserPrayerSettings 获取用户祷告设置
func (*ControllerPrayerTime) GetUserPrayerSettings(ctx context.Context, req *v1.GetUserPrayerSettingsReq) (res *v1.GetUserPrayerSettingsRes, err error) {
	// 构建输入参数
	input := &model.GetUserPrayerSettingsInput{
		UserID: req.UserId,
	}

	// 调用服务层
	output, err := service.PrayerTime().GetUserPrayerSettings(ctx, input)
	if err != nil {
		g.Log().Error(ctx, "GetUserPrayerSettings error:", err)
		return &v1.GetUserPrayerSettingsRes{
			Code: 500,
			Msg:  "Internal server error",
		}, nil
	}

	// 转换默认位置信息
	var defaultLocation *v1.LocationInfo
	if output.DefaultLocation != nil {
		defaultLocation = &v1.LocationInfo{
			Latitude:    output.DefaultLocation.Latitude,
			Longitude:   output.DefaultLocation.Longitude,
			CityName:    output.DefaultLocation.CityName,
			CountryName: output.DefaultLocation.CountryName,
			Timezone:    output.DefaultLocation.Timezone,
		}
	}

	// 转换通知设置
	notifications := make([]*v1.NotificationSetting, 0, len(output.Notifications))
	for _, notification := range output.Notifications {
		notifications = append(notifications, &v1.NotificationSetting{
			PrayerName:    notification.PrayerName,
			Enabled:       notification.Enabled,
			MinutesBefore: notification.MinutesBefore,
			SoundType:     notification.SoundType,
			Vibration:     notification.Vibration,
			OnlyRamadhan:  notification.OnlyRamadhan,
		})
	}

	return &v1.GetUserPrayerSettingsRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.UserPrayerSettings{
			UserId:            output.UserID,
			CalculationMethod: output.CalculationMethod,
			FajrAngle:         output.FajrAngle,
			IshaAngle:         output.IshaAngle,
			AsrMethod:         output.AsrMethod,
			DefaultLocation:   defaultLocation,
			Notifications:     notifications,
		},
	}, nil
}

// UpdateUserPrayerSettings 更新用户祷告设置
func (*ControllerPrayerTime) UpdateUserPrayerSettings(ctx context.Context, req *v1.UpdateUserPrayerSettingsReq) (res *v1.UpdateUserPrayerSettingsRes, err error) {
	// 转换默认位置信息
	var defaultLocation *model.LocationInfo
	if req.DefaultLocation != nil {
		defaultLocation = &model.LocationInfo{
			Latitude:    req.DefaultLocation.Latitude,
			Longitude:   req.DefaultLocation.Longitude,
			CityName:    req.DefaultLocation.CityName,
			CountryName: req.DefaultLocation.CountryName,
			Timezone:    req.DefaultLocation.Timezone,
		}
	}

	// 转换通知设置
	notifications := make([]*model.NotificationSetting, 0, len(req.Notifications))
	for _, notification := range req.Notifications {
		notifications = append(notifications, &model.NotificationSetting{
			PrayerName:    notification.PrayerName,
			Enabled:       notification.Enabled,
			MinutesBefore: notification.MinutesBefore,
			SoundType:     notification.SoundType,
			Vibration:     notification.Vibration,
			OnlyRamadhan:  notification.OnlyRamadhan,
		})
	}

	// 构建输入参数
	input := &model.UpdateUserPrayerSettingsInput{
		UserID:            req.UserId,
		CalculationMethod: req.CalculationMethod,
		FajrAngle:         req.FajrAngle,
		IshaAngle:         req.IshaAngle,
		AsrMethod:         req.AsrMethod,
		DefaultLocation:   defaultLocation,
		Notifications:     notifications,
	}

	// 调用服务层
	err = service.PrayerTime().UpdateUserPrayerSettings(ctx, input)
	if err != nil {
		g.Log().Error(ctx, "UpdateUserPrayerSettings error:", err)
		return &v1.UpdateUserPrayerSettingsRes{
			Code: 500,
			Msg:  "Internal server error",
		}, nil
	}

	return &v1.UpdateUserPrayerSettingsRes{
		Code: 200,
		Msg:  "success",
	}, nil
}
