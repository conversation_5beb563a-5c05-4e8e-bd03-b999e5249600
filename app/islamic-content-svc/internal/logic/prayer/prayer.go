package prayer_time

import (
	"context"
	"fmt"
	"time"

	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/service"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/hablullah/go-prayer"
)

type sPrayer struct{}

func init() {
	service.RegisterPrayer(New())
}

func New() service.IPrayer {
	return &sPrayer{}
}

// GetPrayerTimes 获取祷告时间
func (s *sPrayer) GetPrayerTimes(ctx context.Context, in *model.PrayerTimeInput) (*model.PrayerTimeOutput, error) {
	// 设置默认值
	if in.CalculationMethod == "" {
		in.CalculationMethod = model.DefaultCalculationMethod
	}
	if in.FajrAngle == 0 {
		in.FajrAngle = model.DefaultFajrAngle
	}
	if in.IshaAngle == 0 {
		in.IshaAngle = model.DefaultIshaAngle
	}
	if in.AsrMethod == "" {
		in.AsrMethod = model.DefaultAsrMethod
	}

	// 解析日期
	date, err := time.Parse("2006-01-02", in.Date)
	if err != nil {
		return nil, gerror.Newf("invalid date format: %s", in.Date)
	}

	// 构建计算配置
	config := &model.PrayerCalculationConfig{
		Method:    in.CalculationMethod,
		FajrAngle: in.FajrAngle,
		IshaAngle: in.IshaAngle,
		AsrMethod: in.AsrMethod,
		Latitude:  in.Latitude,
		Longitude: in.Longitude,
		Timezone:  in.Timezone,
		Elevation: in.Elevation,
		Date:      date,
	}

	// 计算祷告时间
	prayerTimes, err := s.CalculatePrayerTimes(ctx, config)
	if err != nil {
		return nil, err
	}

	// 获取位置信息
	location, err := s.GetLocationName(ctx, in.Latitude, in.Longitude)
	if err != nil {
		g.Log().Warning(ctx, "failed to get location name:", err)
		location = &model.LocationInfo{
			Latitude:  in.Latitude,
			Longitude: in.Longitude,
			Timezone:  in.Timezone,
		}
	}

	// 转换为伊斯兰历
	islamicDate, err := s.ConvertToIslamicDate(ctx, in.Date)
	if err != nil {
		g.Log().Warning(ctx, "failed to convert to islamic date:", err)
		islamicDate = &model.IslamicDate{}
	}

	// 判断是否斋月
	isRamadhan, err := s.IsRamadhan(ctx, in.Date)
	if err != nil {
		g.Log().Warning(ctx, "failed to check ramadhan:", err)
		isRamadhan = false
	}

	// 获取当前祷告时间
	current, next, timeToNext := s.GetCurrentPrayer(ctx, prayerTimes)

	return &model.PrayerTimeOutput{
		PrayerTimes:   prayerTimes,
		Location:      location,
		IslamicDate:   islamicDate,
		IsRamadhan:    isRamadhan,
		CurrentPrayer: current,
		NextPrayer:    next,
		TimeToNext:    timeToNext,
	}, nil
}

// CalculatePrayerTimes 计算祷告时间（核心计算方法）
func (s *sPrayer) CalculatePrayerTimes(ctx context.Context, config *model.PrayerCalculationConfig) (*model.PrayerTimes, error) {
	// 解析时区
	loc, err := time.LoadLocation(config.Timezone)
	if err != nil {
		g.Log().Warning(ctx, "failed to load timezone:", config.Timezone, err)
		loc = time.UTC
	}

	// 创建祷告时间计算配置
	var twilightConvention *prayer.TwilightConvention
	switch config.Method {
	case model.MethodMWL:
		twilightConvention = prayer.MWL()
	case model.MethodISNA:
		twilightConvention = prayer.ISNA()
	case model.MethodEgyptian:
		twilightConvention = prayer.Egypt()
	case model.MethodKarachi:
		twilightConvention = prayer.Karachi()
	default:
		twilightConvention = prayer.MWL() // 默认使用MWL
	}

	// 如果有自定义角度，创建自定义配置
	if config.FajrAngle != 0 || config.IshaAngle != 0 {
		fajrAngle := config.FajrAngle
		ishaAngle := config.IshaAngle
		if fajrAngle == 0 {
			fajrAngle = model.DefaultFajrAngle
		}
		if ishaAngle == 0 {
			ishaAngle = model.DefaultIshaAngle
		}
		twilightConvention = &prayer.TwilightConvention{
			FajrAngle: fajrAngle,
			IshaAngle: ishaAngle,
		}
	}

	// 设置晡祷计算方法
	asrConvention := prayer.Shafii
	if config.AsrMethod == model.AsrHanafi {
		asrConvention = prayer.Hanafi
	}

	// 创建计算配置
	prayerConfig := prayer.Config{
		Latitude:           config.Latitude,
		Longitude:          config.Longitude,
		Timezone:           loc,
		TwilightConvention: twilightConvention,
		AsrConvention:      asrConvention,
		PreciseToSeconds:   true,
	}

	// 计算祷告时间
	schedules, err := prayer.Calculate(prayerConfig, config.Date.Year())
	if err != nil {
		return nil, gerror.Wrap(err, "failed to calculate prayer times")
	}

	// 找到指定日期的祷告时间
	var daySchedule prayer.Schedule
	found := false
	targetDate := config.Date.Format("2006-01-02")
	for _, schedule := range schedules {
		if schedule.Date == targetDate {
			daySchedule = schedule
			found = true
			break
		}
	}

	if !found {
		return nil, gerror.New("prayer times not found for the specified date")
	}

	// 格式化时间
	formatTime := func(t time.Time) string {
		return t.Format("15:04")
	}

	prayerTimes := &model.PrayerTimes{
		Subuh:   formatTime(daySchedule.Fajr),
		Terbit:  formatTime(daySchedule.Sunrise),
		Zuhur:   formatTime(daySchedule.Zuhr),
		Ashar:   formatTime(daySchedule.Asr),
		Maghrib: formatTime(daySchedule.Maghrib),
		Isya:    formatTime(daySchedule.Isha),
	}

	// 计算Dhuha时间（日出后15-20分钟）
	dhuhaTime := daySchedule.Sunrise.Add(15 * time.Minute)
	prayerTimes.Dhuha = formatTime(dhuhaTime)

	// 如果是斋月，计算Imsak时间（晨祷前5-10分钟）
	isRamadhan, _ := s.IsRamadhan(ctx, config.Date.Format("2006-01-02"))
	if isRamadhan {
		imsakTime := daySchedule.Fajr.Add(-10 * time.Minute)
		prayerTimes.Imsak = formatTime(imsakTime)
	}

	return prayerTimes, nil
}

// GetUserPrayerSettings 获取用户祷告设置
func (s *sPrayer) GetUserPrayerSettings(ctx context.Context, in *model.GetUserPrayerSettingsInput) (*model.UserPrayerSettingsOutput, error) {
	// TODO: 从数据库获取用户设置
	// 这里先返回默认设置
	return &model.UserPrayerSettingsOutput{
		UserID:            in.UserID,
		CalculationMethod: model.DefaultCalculationMethod,
		FajrAngle:         model.DefaultFajrAngle,
		IshaAngle:         model.DefaultIshaAngle,
		AsrMethod:         model.DefaultAsrMethod,
		DefaultLocation:   nil,
		Notifications:     s.getDefaultNotificationSettings(),
	}, nil
}

// UpdateUserPrayerSettings 更新用户祷告设置
func (s *sPrayer) UpdateUserPrayerSettings(ctx context.Context, in *model.UpdateUserPrayerSettingsInput) error {
	// TODO: 保存用户设置到数据库
	g.Log().Info(ctx, "updating user prayer settings for user:", in.UserID)
	return nil
}

// IsRamadhan 判断指定日期是否为斋月
func (s *sPrayer) IsRamadhan(ctx context.Context, date string) (bool, error) {
	// 解析日期
	_, err := time.Parse("2006-01-02", date)
	if err != nil {
		return false, gerror.Newf("invalid date format: %s", date)
	}

	// 转换为伊斯兰历
	islamicDate, err := s.ConvertToIslamicDate(ctx, date)
	if err != nil {
		return false, err
	}

	// 斋月是伊斯兰历的第9个月
	return islamicDate.Month == 9, nil
}

// ConvertToIslamicDate 转换为伊斯兰历日期
func (s *sPrayer) ConvertToIslamicDate(ctx context.Context, date string) (*model.IslamicDate, error) {
	// 解析公历日期
	gregorianDate, err := time.Parse("2006-01-02", date)
	if err != nil {
		return nil, gerror.Newf("invalid date format: %s", date)
	}

	// 简化的伊斯兰历转换（实际项目中应使用专业的转换库）
	// 这里使用近似算法
	julianDay := s.gregorianToJulian(gregorianDate)
	islamicYear, islamicMonth, islamicDay := s.julianToIslamic(julianDay)

	monthNames := []string{
		"", "Muharram", "Safar", "Rabi' al-awwal", "Rabi' al-thani",
		"Jumada al-awwal", "Jumada al-thani", "Rajab", "Sha'ban",
		"Ramadhan", "Shawwal", "Dhu al-Qi'dah", "Dhu al-Hijjah",
	}

	monthName := ""
	if islamicMonth >= 1 && islamicMonth <= 12 {
		monthName = monthNames[islamicMonth]
	}

	return &model.IslamicDate{
		Year:      int32(islamicYear),
		Month:     int32(islamicMonth),
		Day:       int32(islamicDay),
		MonthName: monthName,
		Formatted: fmt.Sprintf("%d %s %d", islamicDay, monthName, islamicYear),
	}, nil
}

// GetCurrentPrayer 获取当前祷告时间
func (s *sPrayer) GetCurrentPrayer(ctx context.Context, prayerTimes *model.PrayerTimes) (current, next string, timeToNext int32) {
	now := time.Now()
	currentTime := now.Format("15:04")

	prayers := []struct {
		name string
		time string
	}{
		{model.PrayerSubuh, prayerTimes.Subuh},
		{model.PrayerZuhur, prayerTimes.Zuhur},
		{model.PrayerAshar, prayerTimes.Ashar},
		{model.PrayerMaghrib, prayerTimes.Maghrib},
		{model.PrayerIsya, prayerTimes.Isya},
	}

	// 找到当前祷告时间
	for i, prayer := range prayers {
		if currentTime < prayer.time {
			if i == 0 {
				// 当前时间在第一个祷告之前，上一个祷告是昨天的最后一个
				current = model.PrayerIsya
			} else {
				current = prayers[i-1].name
			}
			next = prayer.name

			// 计算到下一个祷告的时间
			nextTime, _ := time.Parse("15:04", prayer.time)
			nextTime = time.Date(now.Year(), now.Month(), now.Day(), nextTime.Hour(), nextTime.Minute(), 0, 0, now.Location())
			if nextTime.Before(now) {
				nextTime = nextTime.Add(24 * time.Hour)
			}
			timeToNext = int32(nextTime.Sub(now).Seconds())
			return
		}
	}

	// 当前时间在最后一个祷告之后
	current = model.PrayerIsya
	next = model.PrayerSubuh

	// 计算到明天晨祷的时间
	nextTime, _ := time.Parse("15:04", prayerTimes.Subuh)
	nextTime = time.Date(now.Year(), now.Month(), now.Day()+1, nextTime.Hour(), nextTime.Minute(), 0, 0, now.Location())
	timeToNext = int32(nextTime.Sub(now).Seconds())

	return
}

// ValidateLocation 验证位置信息
func (s *sPrayer) ValidateLocation(ctx context.Context, latitude, longitude float64) error {
	if latitude < -90 || latitude > 90 {
		return gerror.New("latitude must be between -90 and 90")
	}
	if longitude < -180 || longitude > 180 {
		return gerror.New("longitude must be between -180 and 180")
	}
	return nil
}

// GetLocationName 根据经纬度获取位置名称
func (s *sPrayer) GetLocationName(ctx context.Context, latitude, longitude float64) (*model.LocationInfo, error) {
	// TODO: 集成地理编码服务（如Google Maps API、高德地图API等）
	// 这里先返回基本的位置信息
	return &model.LocationInfo{
		Latitude:    latitude,
		Longitude:   longitude,
		CityName:    "Unknown City",
		CountryName: "Unknown Country",
		Timezone:    "UTC",
	}, nil
}

// getDefaultNotificationSettings 获取默认通知设置
func (s *sPrayer) getDefaultNotificationSettings() []*model.NotificationSetting {
	prayers := []string{
		model.PrayerSubuh, model.PrayerZuhur, model.PrayerAshar,
		model.PrayerMaghrib, model.PrayerIsya,
	}

	settings := make([]*model.NotificationSetting, 0, len(prayers))
	for _, prayer := range prayers {
		settings = append(settings, &model.NotificationSetting{
			PrayerName:    prayer,
			Enabled:       true,
			MinutesBefore: 5,
			SoundType:     model.SoundDefault,
			Vibration:     true,
			OnlyRamadhan:  false,
		})
	}

	// 添加Imsak的默认设置（仅斋月）
	settings = append(settings, &model.NotificationSetting{
		PrayerName:    model.PrayerImsak,
		Enabled:       true,
		MinutesBefore: 0,
		SoundType:     model.SoundDefault,
		Vibration:     true,
		OnlyRamadhan:  true,
	})

	return settings
}

// gregorianToJulian 公历转儒略日
func (s *sPrayer) gregorianToJulian(date time.Time) int {
	year := date.Year()
	month := int(date.Month())
	day := date.Day()

	if month <= 2 {
		year--
		month += 12
	}

	a := year / 100
	b := 2 - a + (a / 4)

	jd := int(365.25*float64(year+4716)) + int(30.6001*float64(month+1)) + day + b - 1524
	return jd
}

// julianToIslamic 儒略日转伊斯兰历
func (s *sPrayer) julianToIslamic(jd int) (year, month, day int) {
	// 伊斯兰历元年对应的儒略日（公元622年7月16日）
	islamicEpoch := 1948439

	// 计算从伊斯兰历元年开始的天数
	daysSinceEpoch := jd - islamicEpoch

	// 伊斯兰历平均年长度（354.367天）
	islamicYearLength := 354.367

	// 计算年份
	year = int(float64(daysSinceEpoch)/islamicYearLength) + 1

	// 计算该年开始的儒略日
	yearStartJD := islamicEpoch + int(float64(year-1)*islamicYearLength)

	// 计算该年内的天数
	dayOfYear := jd - yearStartJD + 1

	// 伊斯兰历月份天数（交替29和30天）
	monthDays := []int{30, 29, 30, 29, 30, 29, 30, 29, 30, 29, 30, 29}

	// 闰年调整（大约每33年有11个闰年）
	if s.isIslamicLeapYear(year) {
		monthDays[11] = 30 // 最后一个月增加一天
	}

	// 计算月份和日期
	month = 1
	for month <= 12 && dayOfYear > monthDays[month-1] {
		dayOfYear -= monthDays[month-1]
		month++
	}

	day = dayOfYear
	return year, month, day
}

// isIslamicLeapYear 判断是否为伊斯兰历闰年
func (s *sPrayer) isIslamicLeapYear(year int) bool {
	// 简化的闰年计算（30年周期中的第2,5,7,10,13,16,18,21,24,26,29年为闰年）
	leapYears := []int{2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29}
	yearInCycle := year % 30
	for _, ly := range leapYears {
		if yearInCycle == ly {
			return true
		}
	}
	return false
}
