package calendar

import (
	"context"
	"fmt"
	"slices"

	"halalplus/app/islamic-content-svc/internal/dao"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/model/entity"
	"halalplus/app/islamic-content-svc/internal/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

type sCalendar struct{}

func init() {
	service.RegisterCalendar(New())
}

func New() service.ICalendar {
	return &sCalendar{}
}

var (
	hijriahCl = dao.CalendarHijriah.Columns()
	eventsCl  = dao.CalendarEvents.Columns()
)

// GetCalendar 获取日历数据
func (s *sCalendar) GetCalendar(ctx context.Context, input *model.CalendarGetInput) ([]*model.CalendarDateInfo, error) {
	// 获取该月的所有日期
	startDate := gtime.NewFromStr(fmt.Sprintf("%d-%02d-01", input.Year, input.Month))
	daysInMonth := startDate.DaysInMonth()

	// 批量查询该月的Hijriah日期数据
	hijriahDataMap, err := s.getHijriahDatesByMonth(ctx, input.Year, input.Month, input.MethodCode, input.DateAdjustment)
	if err != nil {
		g.Log().Error(ctx, "批量获取Hijriah日期失败:", err)
		return nil, err
	}

	// 批量查询该月的事件数据
	eventsMap, err := s.getEventsByMonth(ctx, input.Year, input.Month)
	if err != nil {
		g.Log().Error(ctx, "批量获取事件失败:", err)
		// 事件获取失败不影响日历显示，继续处理
		eventsMap = make(map[int32][]*model.CalendarEventInfo)
	}

	var result []*model.CalendarDateInfo

	for day := 1; day <= daysInMonth; day++ {
		dayInt32 := int32(day)

		// 从批量查询结果中获取Hijriah日期
		hijriahInfo, exists := hijriahDataMap[dayInt32]
		if !exists {
			g.Log().Error(ctx, fmt.Sprintf("未找到%d-%02d-%02d的Hijriah日期数据", input.Year, input.Month, day))
			continue
		}

		// 从批量查询结果中获取事件
		events := eventsMap[dayInt32]

		// 构建日历日期信息
		dateInfo := &model.CalendarDateInfo{
			GregorianYear:  input.Year,
			GregorianMonth: input.Month,
			GregorianDay:   dayInt32,
			HijriahYear:    int32(hijriahInfo.HijriahYear),
			HijriahMonth:   int32(hijriahInfo.HijriahMonth),
			HijriahDay:     int32(hijriahInfo.HijriahDay),
			MethodCode:     input.MethodCode,
			Weekday:        int32(hijriahInfo.Weekday),
			Pasaran:        int32(hijriahInfo.Pasaran),
			WeekdayName:    s.getWeekdayName(int32(hijriahInfo.Weekday)),
			PasaranName:    s.getPasaranName(int32(hijriahInfo.Pasaran)),
			Events:         events,
		}

		result = append(result, dateInfo)
	}

	return result, nil
}

// getHijriahDatesByMonth 批量获取该月的Hijriah日期数据
func (s *sCalendar) getHijriahDatesByMonth(ctx context.Context, year, month int32, methodCode string, adjustment int32) (map[int32]*entity.CalendarHijriah, error) {
	// 先从数据库批量查询该月的数据
	var hijriahDataList []*entity.CalendarHijriah
	err := dao.CalendarHijriah.Ctx(ctx).
		Where(hijriahCl.GregorianYear, year).
		Where(hijriahCl.GregorianMonth, month).
		Where(hijriahCl.MethodCode, methodCode).
		Scan(&hijriahDataList)

	if err != nil {
		g.Log().Error(ctx, "查询Hijriah数据失败:", err)
	}

	hijriahDataMap := make(map[int32]*entity.CalendarHijriah)
	for _, data := range hijriahDataList {
		// 应用日期校正
		if adjustment != 0 {
			s.applyHijriahDateAdjustment(data, adjustment)
		}
		hijriahDataMap[int32(data.GregorianDay)] = data
	}

	return hijriahDataMap, nil
}

// applyHijriahDateAdjustment 应用Hijriah日期校正，正确处理月份边界
func (s *sCalendar) applyHijriahDateAdjustment(hijriahData *entity.CalendarHijriah, adjustment int32) {
	if adjustment == 0 {
		return
	}

	// 获取当前Hijriah月份的天数
	daysInHijriahMonth := s.getDaysInHijriahMonth(hijriahData.HijriahYear, hijriahData.HijriahMonth)

	// 计算调整后的日期
	newDay := hijriahData.HijriahDay + int(adjustment)
	newMonth := hijriahData.HijriahMonth
	newYear := hijriahData.HijriahYear

	// 处理日期溢出（向后调整）
	for newDay > daysInHijriahMonth {
		newDay -= daysInHijriahMonth
		newMonth++
		if newMonth > 12 {
			newMonth = 1
			newYear++
		}
		daysInHijriahMonth = s.getDaysInHijriahMonth(newYear, newMonth)
	}

	// 处理日期下溢（向前调整）
	for newDay <= 0 {
		newMonth--
		if newMonth <= 0 {
			newMonth = 12
			newYear--
		}
		daysInHijriahMonth = s.getDaysInHijriahMonth(newYear, newMonth)
		newDay += daysInHijriahMonth
	}

	// 更新日期
	hijriahData.HijriahDay = newDay
	hijriahData.HijriahMonth = newMonth
	hijriahData.HijriahYear = newYear
}

// getDaysInHijriahMonth 获取Hijriah月份的天数
func (s *sCalendar) getDaysInHijriahMonth(year, month int) int {
	// Hijriah历法中，奇数月份通常有30天，偶数月份有29天
	// 第12个月在闰年有30天，平年有29天
	if month%2 == 1 {
		return 30 // 奇数月：1,3,5,7,9,11月有30天
	} else if month == 12 {
		// 第12个月：闰年30天，平年29天
		if s.isHijriahLeapYear(year) {
			return 30
		}
		return 29
	} else {
		return 29 // 偶数月（除12月外）：2,4,6,8,10月有29天
	}
}

// isHijriahLeapYear 判断是否为Hijriah闰年
func (s *sCalendar) isHijriahLeapYear(year int) bool {
	// Hijriah历法30年周期中有11个闰年
	// 闰年为：2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29
	cycle := year % 30
	leapYears := []int{2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29}

	return slices.Contains(leapYears, cycle)
}

// getEventsByMonth 批量获取该月的事件数据
func (s *sCalendar) getEventsByMonth(ctx context.Context, year, month int32) (map[int32][]*model.CalendarEventInfo, error) {
	var events []*entity.CalendarEvents
	err := dao.CalendarEvents.Ctx(ctx).
		Where(eventsCl.GregorianYear, year).
		Where(eventsCl.GregorianMonth, month).
		Scan(&events)

	if err != nil {
		return nil, err
	}

	eventsMap := make(map[int32][]*model.CalendarEventInfo)
	for _, event := range events {
		eventInfo := &model.CalendarEventInfo{
			Id:          int64(event.Id),
			EventType:   event.EventType,
			Title:       event.Title,
			Description: event.Description,
			JumpUrl:     event.JumpUrl,
		}

		day := int32(event.GregorianDay)
		eventsMap[day] = append(eventsMap[day], eventInfo)
	}

	return eventsMap, nil
}

// getWeekdayName 获取星期名称
func (s *sCalendar) getWeekdayName(weekday int32) string {
	weekdayNames := []string{"Ahad", "Senin", "Selasa", "Rabu", "Kamis", "Jumat", "Sabtu"}
	if weekday >= 0 && weekday < int32(len(weekdayNames)) {
		return weekdayNames[weekday]
	}
	return ""
}

// getPasaranName 获取Pasaran名称
func (s *sCalendar) getPasaranName(pasaran int32) string {
	pasaranNames := []string{"Kliwon", "Legi", "Pahing", "Pon", "Wage"}
	if pasaran >= 0 && pasaran < int32(len(pasaranNames)) {
		return pasaranNames[pasaran]
	}
	return ""
}
