package model

import (
	"time"
)

// PrayerTimeInput 祷告时间查询输入参数
type PrayerTimeInput struct {
	Latitude          float64 `json:"latitude" v:"required|between:-90,90#纬度不能为空|纬度必须在-90到90之间"`
	Longitude         float64 `json:"longitude" v:"required|between:-180,180#经度不能为空|经度必须在-180到180之间"`
	Date              string  `json:"date" v:"required|date-format:Y-m-d#日期不能为空|日期格式必须为YYYY-MM-DD"`
	Timezone          string  `json:"timezone" v:"required#时区不能为空"`
	CalculationMethod string  `json:"calculation_method" v:"in:MWL,ISNA,Egyptian,Karachi#计算方法必须为MWL,ISNA,Egyptian,Karachi之一"`
	FajrAngle         float64 `json:"fajr_angle" v:"between:10,25#晨祷角度必须在10到25度之间"`
	IshaAngle         float64 `json:"isha_angle" v:"between:10,25#宵祷角度必须在10到25度之间"`
	AsrMethod         string  `json:"asr_method" v:"in:Standard,Hanafi#晡祷计算方法必须为Standard或Hanafi"`
	Elevation         float64 `json:"elevation" v:"min:0#海拔高度不能为负数"`
}

// PrayerTimeOutput 祷告时间查询输出结果
type PrayerTimeOutput struct {
	PrayerTimes   *PrayerTimes  `json:"prayer_times"`
	Location      *LocationInfo `json:"location"`
	IslamicDate   *IslamicDate  `json:"islamic_date"`
	IsRamadhan    bool          `json:"is_ramadhan"`
	CurrentPrayer string        `json:"current_prayer"`
	NextPrayer    string        `json:"next_prayer"`
	TimeToNext    int32         `json:"time_to_next"`
}

// PrayerTimes 祷告时间
type PrayerTimes struct {
	Imsak   string `json:"imsak"`   // 伊姆萨克时间（仅斋月期间）
	Subuh   string `json:"subuh"`   // 晨祷时间
	Terbit  string `json:"terbit"`  // 日出时间
	Dhuha   string `json:"dhuha"`   // 上午祷告时间（可选）
	Zuhur   string `json:"zuhur"`   // 晌祷时间
	Ashar   string `json:"ashar"`   // 晡祷时间
	Maghrib string `json:"maghrib"` // 昏祷时间
	Isya    string `json:"isya"`    // 宵祷时间
}

// LocationInfo 位置信息
type LocationInfo struct {
	Latitude    float64 `json:"latitude"`
	Longitude   float64 `json:"longitude"`
	CityName    string  `json:"city_name"`
	CountryName string  `json:"country_name"`
	Timezone    string  `json:"timezone"`
}

// IslamicDate 伊斯兰历日期
type IslamicDate struct {
	Year      int32  `json:"year"`
	Month     int32  `json:"month"`
	Day       int32  `json:"day"`
	MonthName string `json:"month_name"`
	Formatted string `json:"formatted"`
}

// UserPrayerSettingsInput 用户祷告设置输入参数
type UserPrayerSettingsInput struct {
	UserID            int64                  `json:"user_id" v:"required|min:1#用户ID不能为空|用户ID必须大于0"`
	CalculationMethod string                 `json:"calculation_method" v:"in:MWL,ISNA,Egyptian,Karachi#计算方法必须为MWL,ISNA,Egyptian,Karachi之一"`
	FajrAngle         float64                `json:"fajr_angle" v:"between:10,25#晨祷角度必须在10到25度之间"`
	IshaAngle         float64                `json:"isha_angle" v:"between:10,25#宵祷角度必须在10到25度之间"`
	AsrMethod         string                 `json:"asr_method" v:"in:Standard,Hanafi#晡祷计算方法必须为Standard或Hanafi"`
	DefaultLocation   *LocationInfo          `json:"default_location"`
	Notifications     []*NotificationSetting `json:"notifications"`
}

// UserPrayerSettingsOutput 用户祷告设置输出结果
type UserPrayerSettingsOutput struct {
	UserID            int64                  `json:"user_id"`
	CalculationMethod string                 `json:"calculation_method"`
	FajrAngle         float64                `json:"fajr_angle"`
	IshaAngle         float64                `json:"isha_angle"`
	AsrMethod         string                 `json:"asr_method"`
	DefaultLocation   *LocationInfo          `json:"default_location"`
	Notifications     []*NotificationSetting `json:"notifications"`
}

// NotificationSetting 通知设置
type NotificationSetting struct {
	PrayerName    string `json:"prayer_name" v:"required|in:imsak,subuh,terbit,dhuha,zuhur,ashar,maghrib,isya#祷告名称不能为空|祷告名称必须为有效值"`
	Enabled       bool   `json:"enabled"`
	MinutesBefore int32  `json:"minutes_before" v:"between:0,30#提前通知时间必须在0到30分钟之间"`
	SoundType     string `json:"sound_type" v:"in:none,default,tarhim,bedug,kentongan,adhan#声音类型必须为有效值"`
	Vibration     bool   `json:"vibration"`
	OnlyRamadhan  bool   `json:"only_ramadhan"`
}

// PrayerCalculationConfig 祷告时间计算配置
type PrayerCalculationConfig struct {
	Method    string    `json:"method"`     // 计算方法: "MWL", "ISNA", "Egyptian", "Karachi"
	FajrAngle float64   `json:"fajr_angle"` // 晨祷角度: 18° (可调整)
	IshaAngle float64   `json:"isha_angle"` // 宵祷角度: 17° (可调整)
	AsrMethod string    `json:"asr_method"` // "Standard" 或 "Hanafi"
	Latitude  float64   `json:"latitude"`   // 用户位置纬度
	Longitude float64   `json:"longitude"`  // 用户位置经度
	Timezone  string    `json:"timezone"`   // 时区
	Elevation float64   `json:"elevation"`  // 海拔高度 (可选)
	Date      time.Time `json:"date"`       // 计算日期
}

// GetUserPrayerSettingsInput 获取用户祷告设置输入参数
type GetUserPrayerSettingsInput struct {
	UserID int64 `json:"user_id" v:"required|min:1#用户ID不能为空|用户ID必须大于0"`
}

// UpdateUserPrayerSettingsInput 更新用户祷告设置输入参数
type UpdateUserPrayerSettingsInput struct {
	UserID            int64                  `json:"user_id" v:"required|min:1#用户ID不能为空|用户ID必须大于0"`
	CalculationMethod string                 `json:"calculation_method" v:"in:MWL,ISNA,Egyptian,Karachi#计算方法必须为MWL,ISNA,Egyptian,Karachi之一"`
	FajrAngle         float64                `json:"fajr_angle" v:"between:10,25#晨祷角度必须在10到25度之间"`
	IshaAngle         float64                `json:"isha_angle" v:"between:10,25#宵祷角度必须在10到25度之间"`
	AsrMethod         string                 `json:"asr_method" v:"in:Standard,Hanafi#晡祷计算方法必须为Standard或Hanafi"`
	DefaultLocation   *LocationInfo          `json:"default_location"`
	Notifications     []*NotificationSetting `json:"notifications"`
}

// 默认配置常量
const (
	DefaultCalculationMethod = "MWL"
	DefaultFajrAngle         = 18.0
	DefaultIshaAngle         = 17.0
	DefaultAsrMethod         = "Standard"
)

// 祷告名称常量
const (
	PrayerImsak   = "imsak"
	PrayerSubuh   = "subuh"
	PrayerTerbit  = "terbit"
	PrayerDhuha   = "dhuha"
	PrayerZuhur   = "zuhur"
	PrayerAshar   = "ashar"
	PrayerMaghrib = "maghrib"
	PrayerIsya    = "isya"
)

// 声音类型常量
const (
	SoundNone      = "none"
	SoundDefault   = "default"
	SoundTarhim    = "tarhim"    // 诵经声
	SoundBedug     = "bedug"     // 鼓声
	SoundKentongan = "kentongan" // 竹筒声
	SoundAdhan     = "adhan"     // 宣礼声
)

// 计算方法常量
const (
	MethodMWL      = "MWL"      // Muslim World League
	MethodISNA     = "ISNA"     // Islamic Society of North America
	MethodEgyptian = "Egyptian" // Egyptian General Authority of Survey
	MethodKarachi  = "Karachi"  // University of Islamic Sciences, Karachi
)

// 晡祷计算方法常量
const (
	AsrStandard = "Standard" // 标准方法
	AsrHanafi   = "Hanafi"   // 哈纳菲方法
)
