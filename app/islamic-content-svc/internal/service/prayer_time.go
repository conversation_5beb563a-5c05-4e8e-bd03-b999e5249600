// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"halalplus/app/islamic-content-svc/internal/model"
)

type (
	IPrayerTime interface {
		// GetPrayerTimes 获取祷告时间
		GetPrayerTimes(ctx context.Context, in *model.PrayerTimeInput) (*model.PrayerTimeOutput, error)
		
		// GetUserPrayerSettings 获取用户祷告设置
		GetUserPrayerSettings(ctx context.Context, in *model.GetUserPrayerSettingsInput) (*model.UserPrayerSettingsOutput, error)
		
		// UpdateUserPrayerSettings 更新用户祷告设置
		UpdateUserPrayerSettings(ctx context.Context, in *model.UpdateUserPrayerSettingsInput) error
		
		// CalculatePrayerTimes 计算祷告时间（核心计算方法）
		CalculatePrayerTimes(ctx context.Context, config *model.PrayerCalculationConfig) (*model.PrayerTimes, error)
		
		// IsRamadhan 判断指定日期是否为斋月
		IsRamadhan(ctx context.Context, date string) (bool, error)
		
		// ConvertToIslamicDate 转换为伊斯兰历日期
		ConvertToIslamicDate(ctx context.Context, date string) (*model.IslamicDate, error)
		
		// GetCurrentPrayer 获取当前祷告时间
		GetCurrentPrayer(ctx context.Context, prayerTimes *model.PrayerTimes) (current, next string, timeToNext int32)
		
		// ValidateLocation 验证位置信息
		ValidateLocation(ctx context.Context, latitude, longitude float64) error
		
		// GetLocationName 根据经纬度获取位置名称
		GetLocationName(ctx context.Context, latitude, longitude float64) (*model.LocationInfo, error)
	}
)

var (
	localPrayerTime IPrayerTime
)

func PrayerTime() IPrayerTime {
	if localPrayerTime == nil {
		panic("implement not found for interface IPrayerTime, forgot register?")
	}
	return localPrayerTime
}

func RegisterPrayerTime(i IPrayerTime) {
	localPrayerTime = i
}
