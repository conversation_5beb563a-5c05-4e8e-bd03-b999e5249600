syntax = "proto3";

package islamic.v1;
option go_package = "halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1";

import "common/base.proto";

// 祷告时间查询请求
message GetPrayerTimesReq {
  double latitude = 1;                    // 纬度
  double longitude = 2;                   // 经度
  string date = 3;                        // 日期 YYYY-MM-DD 格式
  string timezone = 4;                    // 时区，如 "Asia/Shanghai"
  string calculation_method = 5;          // 计算方法: "MWL", "ISNA", "Egyptian", "Karachi"
  double fajr_angle = 6;                  // 晨祷角度，默认18.0
  double isha_angle = 7;                  // 宵祷角度，默认17.0
  string asr_method = 8;                  // 晡祷计算方法: "Standard" 或 "Hanafi"
  double elevation = 9;                   // 海拔高度（米），可选
}

// 祷告时间查询响应
message GetPrayerTimesRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  PrayerTimesData data = 4;
}

// 祷告时间数据
message PrayerTimesData {
  PrayerTimes prayer_times = 1;           // 祷告时间
  LocationInfo location = 2;              // 位置信息
  IslamicDate islamic_date = 3;           // 伊斯兰历日期
  bool is_ramadhan = 4;                   // 是否斋月
  string current_prayer = 5;              // 当前祷告名称
  string next_prayer = 6;                 // 下一个祷告名称
  int32 time_to_next = 7;                 // 距离下个祷告的秒数
}

// 祷告时间
message PrayerTimes {
  string imsak = 1;                       // 伊姆萨克时间（仅斋月期间）
  string subuh = 2;                       // 晨祷时间
  string terbit = 3;                      // 日出时间
  string dhuha = 4;                       // 上午祷告时间（可选）
  string zuhur = 5;                       // 晌祷时间
  string ashar = 6;                       // 晡祷时间
  string maghrib = 7;                     // 昏祷时间
  string isya = 8;                        // 宵祷时间
}

// 位置信息
message LocationInfo {
  double latitude = 1;                    // 纬度
  double longitude = 2;                   // 经度
  string city_name = 3;                   // 城市名称
  string country_name = 4;                // 国家名称
  string timezone = 5;                    // 时区
}

// 伊斯兰历日期
message IslamicDate {
  int32 year = 1;                         // 伊斯兰历年份
  int32 month = 2;                        // 伊斯兰历月份
  int32 day = 3;                          // 伊斯兰历日期
  string month_name = 4;                  // 月份名称
  string formatted = 5;                   // 格式化的日期字符串
}


// 祷告时间服务定义
service PrayerTimeService {
  // 获取祷告时间
  rpc GetPrayerTimes(GetPrayerTimesReq) returns (GetPrayerTimesRes);
}
