syntax = "proto3";

package islamic.v1;
option go_package = "halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1";

import "common/base.proto";

// 祷告时间查询请求
message GetPrayerTimesReq {
  double latitude = 1;                    // 纬度
  double longitude = 2;                   // 经度
  string timezone = 3;                    // 时区，如 "Asia/Shanghai"
  string method_code = 4;                 // 计算方法：AUTO, LFNU, UMMUL_QURA (这个设置是在日历那边，用于返回伊斯兰日期)
  int32 date_adjustment = 5;              // 日期校正：-3到+3天的偏移量 (这个设置是在日历那边)
}

// 祷告时间查询响应
message GetPrayerTimesRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  PrayerTimesData data = 4;
}

// 祷告时间数据
message PrayerTimesData {
  PrayerTimes prayer_times = 1;           // 祷告时间
  IslamicDate islamic_date = 2;           // 伊斯兰历日期
  // bool is_ramadhan = 3;                   // 是否斋月
}

// 祷告时间
message PrayerTimes {
  string imsak = 1;                       // 伊姆萨克时间（仅斋月期间）
  string subuh = 2;                       // 晨祷时间
  string terbit = 3;                      // 日出时间
  string dhuha = 4;                       // 上午祷告时间（可选，目前还不准确）
  string zuhur = 5;                       // 晌祷时间
  string ashar = 6;                       // 晡祷时间
  string maghrib = 7;                     // 昏祷时间
  string isya = 8;                        // 宵祷时间
}

// 伊斯兰历日期
message IslamicDate {
  int32 year = 1;                         // 伊斯兰历年份
  int32 month = 2;                        // 伊斯兰历月份
  int32 day = 3;                          // 伊斯兰历日期
}


// 祷告时间服务定义
service PrayerTimeService {
  // 获取祷告时间
  rpc GetPrayerTimes(GetPrayerTimesReq) returns (GetPrayerTimesRes);
}
