# 伊斯兰祷告时间功能需求文档

## 1. 功能概述

为HalalPlus平台的islamic-content-svc微服务开发完整的伊斯兰祷告时间功能，包括祷告时间计算、显示、通知提醒等核心功能。

## 2. 核心功能需求

### 2.1 祷告时间计算与显示

#### 2.1.1 祷告时间类型
```
必须显示的祷告时间：
- Subuh (晨祷) - 黎明前，太阳在地平线下18°
- Zuh<PERSON> (晌祷) - 正午后太阳开始偏西
- Ashar (晡祷) - 下午，影子长度等于物体高度+正午影长
- Maghrib (昏祷) - 日落时刻
- <PERSON>ya (宵祷) - 太阳在地平线下17-18°

条件显示的时间：
- <PERSON><PERSON><PERSON> (伊姆萨克) - 仅斋月期间显示，晨祷前5-10分钟
- <PERSON>huha (上午祷告) - 可选显示，日出后15-20分钟
- Terbit (日出) - 参考时间，日出时刻
```

#### 2.1.2 计算参数
```go
type PrayerCalculationConfig struct {
    Method      string  // 计算方法: "MWL", "ISNA", "Egyptian", "Karachi"
    FajrAngle   float64 // 晨祷角度: 18° (可调整)
    IshaAngle   float64 // 宵祷角度: 17° (可调整) 
    AsrMethod   string  // "Standard" 或 "Hanafi"
    Latitude    float64 // 用户位置纬度
    Longitude   float64 // 用户位置经度
    Timezone    int     // 时区偏移
    Elevation   float64 // 海拔高度 (可选)
}
```

### 2.2 用户界面需求

#### 2.2.1 主界面布局
```
顶部区域：
├── 地理位置显示 (如: "天河区，广州市")
├── 当前祷告时间突出显示 (如: "Ashar 15:55 CST")
├── 倒计时显示 (如: "-00:57:40")
└── 更新按钮 + 朝向指示按钮

日期区域：
├── 公历日期 (如: "31 Juli 2025")
├── 伊斯兰历日期 (如: "9 Safar 1447")
└── 日期导航 (前一天/后一天)

祷告时间列表：
├── 每个祷告时间一行
├── 图标 + 名称 + 时间 + 状态指示器
├── 当前祷告时间高亮显示
└── 通知状态图标 (铃铛/静音/禁用)
```

#### 2.2.2 时间显示格式
```
时间格式: HH:MM (24小时制)
时区显示: 跟随系统时区，显示时区缩写
状态指示器:
- 🔕 已过时间 (灰色)
- 🔔 开启通知
- 🔇 静音通知  
- 📱 震动通知
- ⚫ 禁用通知
```

### 2.3 地理位置功能

#### 2.3.1 位置获取
```go
type LocationService interface {
    GetCurrentLocation() (*Location, error)
    GetLocationName(lat, lng float64) (string, error)
    SaveUserLocation(userID int, location *Location) error
    GetUserLocation(userID int) (*Location, error)
}

type Location struct {
    Latitude    float64 `json:"latitude"`
    Longitude   float64 `json:"longitude"`
    CityName    string  `json:"city_name"`
    CountryName string  `json:"country_name"`
    Timezone    string  `json:"timezone"`
    UpdatedAt   time.Time `json:"updated_at"`
}
```

#### 2.3.2 位置管理需求
- 自动获取用户GPS位置 (需用户授权)
- 手动搜索和选择城市
- 保存用户常用位置列表
- 位置变化时自动更新祷告时间
- 离线时使用上次保存的位置

### 2.4 通知系统需求

#### 2.4.1 通知类型配置
```go
type NotificationConfig struct {
    PrayerName    string           `json:"prayer_name"`
    Enabled       bool            `json:"enabled"`
    NotifyBefore  int             `json:"notify_before_minutes"` // 提前通知分钟数
    SoundType     NotificationSound `json:"sound_type"`
    VibrationEnabled bool         `json:"vibration_enabled"`
    OnlyRamadhan  bool            `json:"only_ramadhan"`        // 仅斋月期间
}

type NotificationSound string
const (
    SoundNone      NotificationSound = "none"
    SoundDefault   NotificationSound = "default"
    SoundTarhim    NotificationSound = "tarhim"    // 诵经声
    SoundBedug     NotificationSound = "bedug"     // 鼓声
    SoundKentongan NotificationSound = "kentongan" // 竹筒声
    SoundAdhan     NotificationSound = "adhan"     // 宣礼声
)
```

#### 2.4.2 通知设置界面需求
```
通知总开关
├── 全部启用/禁用

每个祷告时间的独立设置：
├── 开启/关闭通知
├── 通知声音选择
├── 震动开关
├── 提前通知时间 (0-30分钟)
└── 仅斋月期间选项 (针对Imsak)

特殊设置：
├── 勿扰时间段设置
├── 音量大小调节
└── 通知重复次数
```

### 2.5 斋月功能需求

#### 2.5.1 斋月检测
```go
type RamadhanService interface {
    IsRamadhan(date time.Time) bool
    GetRamadhanPeriod(year int) (start, end time.Time, error)
    GetIfterTime(date time.Time, location *Location) time.Time
    GetSuhurTime(date time.Time, location *Location) time.Time
}
```

#### 2.5.2 斋月特殊功能
- Imsak时间显示和通知
- 开斋时间 (Maghrib) 特殊标记
- 封斋时间 (Imsak) 倒计时
- 斋月日历集成
- 斋月专用通知音效

### 2.6 API接口需求

#### 2.6.1 祷告时间查询接口
```protobuf
// 获取祷告时间
service PrayerTimeService {
    rpc GetPrayerTimes(GetPrayerTimesReq) returns (GetPrayerTimesRes);
    rpc GetUserPrayerSettings(GetUserSettingsReq) returns (GetUserSettingsRes);
    rpc UpdatePrayerSettings(UpdateSettingsReq) returns (UpdateSettingsRes);
}

message GetPrayerTimesReq {
    double latitude = 1;
    double longitude = 2;
    string date = 3;           // YYYY-MM-DD format
    string timezone = 4;
    string calculation_method = 5;
}

message GetPrayerTimesRes {
    PrayerTimes prayer_times = 1;
    LocationInfo location = 2;
    IslamicDate islamic_date = 3;
    bool is_ramadhan = 4;
}

message PrayerTimes {
    string imsak = 1;    // 仅斋月期间有值
    string subuh = 2;
    string terbit = 3;
    string dhuha = 4;
    string zuhur = 5;
    string ashar = 6;
    string maghrib = 7;
    string isya = 8;
    string current_prayer = 9;  // 当前祷告名称
    string next_prayer = 10;    // 下一个祷告名称
    int32 time_to_next = 11;    // 距离下个祷告的秒数
}
```

#### 2.6.2 用户设置接口
```protobuf
message UpdateSettingsReq {
    int64 user_id = 1;
    repeated NotificationSetting notifications = 2;
    LocationInfo default_location = 3;
    string calculation_method = 4;
}

message NotificationSetting {
    string prayer_name = 1;
    bool enabled = 2;
    int32 minutes_before = 3;
    string sound_type = 4;
    bool vibration = 5;
    bool only_ramadhan = 6;
}
```

### 2.7 数据存储需求

#### 2.7.1 数据库表设计
```sql
-- 用户祷告设置表
CREATE TABLE user_prayer_settings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    calculation_method VARCHAR(20) DEFAULT 'MWL',
    fajr_angle DECIMAL(4,2) DEFAULT 18.0,
    isha_angle DECIMAL(4,2) DEFAULT 17.0,
    asr_method VARCHAR(10) DEFAULT 'Standard',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id)
);

-- 用户通知设置表
CREATE TABLE user_notification_settings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    prayer_name VARCHAR(20) NOT NULL,
    enabled BOOLEAN DEFAULT TRUE,
    minutes_before INT DEFAULT 0,
    sound_type VARCHAR(20) DEFAULT 'default',
    vibration_enabled BOOLEAN DEFAULT TRUE,
    only_ramadhan BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_prayer (user_id, prayer_name)
);

-- 用户位置信息表
CREATE TABLE user_locations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    latitude DECIMAL(10,8) NOT NULL,
    longitude DECIMAL(11,8) NOT NULL,
    city_name VARCHAR(100),
    country_name VARCHAR(100),
    timezone VARCHAR(50),
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id)
);
```

## 3. 技术实现要求

### 3.1 计算库选择
- **推荐使用**: github.com/hablullah/go-prayer (Go语言)
- **备选方案**: 自实现天文计算算法
- **精度要求**: 时间误差不超过±2分钟

### 3.2 缓存策略
```go
// 祷告时间缓存 (按地区+日期缓存)
type PrayerTimeCache struct {
    Key string `redis:"key"` // format: "prayer_times:{lat}:{lng}:{date}"
    PrayerTimes PrayerTimes `redis:"prayer_times"`
    TTL time.Duration `redis:"ttl"` // 24小时过期
}
```

### 3.3 错误处理
- 位置获取失败时使用默认位置或上次保存位置
- 网络异常时使用本地缓存数据
- 计算失败时提供降级方案
- 用户友好的错误提示信息

### 3.4 性能要求
- 祷告时间计算响应时间 < 100ms
- 支持离线模式 (本地缓存7天数据)
- 通知发送延迟 < 5秒
- 界面加载时间 < 2秒

## 4. 测试要求

### 4.1 功能测试
- 不同地理位置的时间计算准确性
- 斋月期间Imsak时间显示
- 通知功能的准确性和及时性
- 时区切换的正确性

### 4.2 边界测试
- 极地地区的特殊情况处理
- 夏令时切换期间的时间计算
- 伊斯兰历转换的准确性
- 大量并发用户的性能测试

### 4.3 用户体验测试
- 界面响应速度
- 通知音效和震动效果
- 多语言支持 (阿拉伯语、英语、印尼语、中文)
- 无障碍访问支持

## 5. 部署和运维

### 5.1 监控指标
- 祷告时间计算准确率
- 通知发送成功率
- API响应时间
- 用户活跃度统计

### 5.2 配置管理
- 计算方法参数可配置
- 通知音效文件管理
- 多地区时区数据更新
- 伊斯兰历数据维护

## 6. 多平台适配

### 6.1 移动端适配
- iOS/Android原生通知集成
- 后台运行时的通知推送
- 低功耗模式优化
- 系统权限管理

### 6.2 Web端适配
- 浏览器通知API集成
- PWA离线功能支持
- 响应式布局设计
- 跨浏览器兼容性

## 7. 开发优先级

### 7.1 第一阶段 (核心功能)
- [ ] 祷告时间计算引擎
- [ ] 基础API接口实现
- [ ] 数据库表结构创建
- [ ] 位置服务集成

### 7.2 第二阶段 (用户功能)
- [ ] 用户设置管理
- [ ] 通知系统实现
- [ ] 斋月功能支持
- [ ] 缓存机制优化

### 7.3 第三阶段 (增强功能)
- [ ] 多语言支持
- [ ] 高级通知选项
- [ ] 离线功能支持
- [ ] 性能优化

## 8. 关键技术点

### 8.1 时间计算精度
```go
// 使用高精度浮点数进行天文计算
type AstronomicalCalculator struct {
    precision float64 // 计算精度控制
}

// 考虑大气折射、海拔等影响因子
func (calc *AstronomicalCalculator) CalculateSunPosition(
    lat, lng float64, 
    date time.Time,
    elevation float64,
) SunPosition {
    // 实现高精度太阳位置计算
}
```

### 8.2 伊斯兰历转换
```go
type IslamicCalendar struct {
    // 使用 Umm al-Qura 日历系统
    calendar string
}

func (ic *IslamicCalendar) ConvertToIslamic(gregorianDate time.Time) IslamicDate {
    // 公历转伊斯兰历
}

func (ic *IslamicCalendar) IsRamadhan(date time.Time) bool {
    islamicDate := ic.ConvertToIslamic(date)
    return islamicDate.Month == 9 // Ramadhan是第9个月
}
```

### 8.3 通知调度
```go
type NotificationScheduler interface {
    SchedulePrayerNotifications(userID int64, prayerTimes PrayerTimes) error
    CancelScheduledNotifications(userID int64) error
    SendImmediateNotification(userID int64, prayer string) error
}

// 与notify-svc微服务集成
type NotifyServiceClient struct {
    client notify.NotifyServiceClient
}
```

---

**文档版本**: v1.0  
**创建日期**: 2025-01-31  
**适用项目**: HalalPlus islamic-content-svc  
**文档状态**: 待评审  

此需求文档涵盖了伊斯兰祷告时间功能的完整实现要求，可作为后续AI开发的详细参考指南。